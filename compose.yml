version: '3.8'

services:
  db:
    image: bitnami/postgresql:latest
    container_name: monorepo_postgres
    environment:
      - POSTGRESQL_USERNAME=${POSTGRESQL_USERNAME:-postgres}
      - POSTGRESQL_PASSWORD=${POSTGRESQL_PASSWORD:-postgres}
      - POSTGRESQL_DATABASE=${POSTGRESQL_DATABASE:-postgres}
    ports:
      - '${POSTGRESQL_PORT:-5432}:5432'
    volumes:
      - postgres_data:/bitnami/postgresql
  redis:
    image: bitnami/redis:latest
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redispassword}
    ports:
      - '6377:6379'
    volumes:
      - redis_data:/bitnami/redis

volumes:
  postgres_data:
  redis_data:
