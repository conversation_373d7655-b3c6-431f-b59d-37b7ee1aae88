{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "overrides": [{"env": {"node": true}, "files": [".eslintrc.{js,cjs}"], "parserOptions": {"sourceType": "script"}}], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"prettier/prettier": ["error", {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 80, "endOfLine": "lf"}], "@typescript-eslint/no-explicit-any": "off"}}