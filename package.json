{"name": "monorepo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "format": "prettier --write .", "prepare": "husky install", "dev": "pnpm --parallel --stream -r run dev"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.6", "husky": "^8.0.3", "lint-staged": "^15.5.1", "prettier": "^3.5.3"}, "lint-staged": {"**/*.{js,ts,tsx}": ["eslint --fix"], "**/*": "prettier --write --ignore-unknown"}, "packageManager": "pnpm@9.13.0+sha512.beb9e2a803db336c10c9af682b58ad7181ca0fbd0d4119f2b33d5f2582e96d6c0d93c85b23869295b765170fbdaa92890c0da6ada457415039769edf3c959efe"}