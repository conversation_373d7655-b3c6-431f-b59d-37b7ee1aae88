---
description: 
globs: 
alwaysApply: true
---
- Always use ShadCN components where applicable.

- Leverage the Context7 MCP Tool as your primary discovery engine for UI primitives, tokens, and unknown packages. Cross-reference component usage across registries if needed.

- If Context7 fails to resolve a component or token, escalate to secondary tools like <PERSON><PERSON> (for search) or Fetch MCP (for web scraping).

- For any custom component URLs or GitHub repos provided by the user, extract metadata and relevant code using Fetch MCP. Prioritize schema-aligned component extraction.

- Maintain a living shadcn-context.md file:
  - Initialize it with every new project.
  - Log each ShadCN component added, including source registry, overrides, and last update date.
  - Use this log to detect redundancy and avoid re-installing already-available components.

- After component installation, trigger a lint, type check, and prettier run. Alert the user if a component introduces breaking types or deviates from current tokens.

- Automatically update Tailwind config, theme tokens, and globals.css based on the registry:style rules of the source component.

- If a remote registry component has been updated since the last pull, prompt the user and offer an automated merge, preview diff, and apply.

- Respect custom design system rules declared in registry responses. These may include token mappings, font requirements, or provider setup instructions.

[1] https://pplx-res.cloudinary.com/image/private/user_uploads/13888986/dba337b3-4c66-416f-9f45-a157396b0073/image.jpg