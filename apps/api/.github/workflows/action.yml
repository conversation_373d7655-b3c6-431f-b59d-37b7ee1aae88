name: Update Domain Lists
on:
  schedule:
    - cron: "0 0 * * *" # Runs daily at 00:00 UTC
  workflow_dispatch: # Allow manual triggers

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Required for git history

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 24

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm install node-fetch p-queue whoiser

      - name: Run domain update script
        run: node scripts/update.ts

      - name: Move disposable domains to src
        run: |
          mkdir -p src
          mv active_domains.json src/config/disposable-domains.json

      - name: Add new file to git
        run: git add src/config/disposable-domains.json

      - name: Check for changes
        id: changes
        run: |
          git diff --quiet || echo "has_changes=true" >> $GITHUB_OUTPUT

      - name: Commit and push changes
        if: ${{ success() && steps.changes.outputs.has_changes == 'true' }}
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '41898282+github-actions[bot]@users.noreply.github.com'
          git commit -m "chore: update disposable domains list"
          git push
