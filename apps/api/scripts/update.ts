import fs from "fs/promises";
import fetch from "node-fetch";
import PQueue from "p-queue";
import { whoisDomain } from "whoiser";

// Initialize a queue with a concurrency of 5 to avoid rate-limiting
const queue = new PQueue({
  concurrency: 50,
});

const SOURCES = [
  "https://raw.githubusercontent.com/GeroldSetz/emailondeck.com-domains/refs/heads/master/emailondeck.com_domains_from_bdea.cc.txt",
  "https://raw.githubusercontent.com/flotwig/disposable-email-addresses/refs/heads/master/domains.txt",
  "https://raw.githubusercontent.com/7c/fakefilter/refs/heads/main/txt/data.txt",
  "https://raw.githubusercontent.com/FGRibreau/mailchecker/refs/heads/master/list.txt",
  "https://raw.githubusercontent.com/disposable-email-domains/disposable-email-domains/refs/heads/main/disposable_email_blocklist.conf",
  "https://raw.githubusercontent.com/wesbos/burner-email-providers/refs/heads/master/emails.txt",
  "https://www.stopforumspam.com/downloads/toxic_domains_whole.txt",
];

// Pre-filter known expired or invalid domains to save API calls
const PRE_FILTERED_DOMAINS = ["expired-domain.com", "old-temporary.com"];

/**
 * Fetches a list of domains from a given URL.
 * Handles both plain text and JSON responses.
 * @param {string} url The URL of the domain list source.
 * @returns {Promise<string[]>} A promise that resolves to an array of domains.
 */
async function fetchDomainsFromSource(url: string): Promise<string[]> {
  try {
    const res = await fetch(url);
    if (!res.ok) {
      console.error(`Failed to fetch from ${url}, status: ${res.status}`);
      return [];
    }
    const text = await res.text();
    // Split by newlines, trim, and filter out comments/empty lines
    return text
      .split(/\r?\n/)
      .map((l: string) => l.trim().toLowerCase())
      .filter((l: string) => l && !l.startsWith("#"));
  } catch (error) {
    console.error(`Error fetching or parsing source ${url}:`, error);
    return [];
  }
}

/**
 * Fetches WHOIS data for a single domain to check its expiration status.
 * Includes robust error handling.
 * @param {string} domain The domain to check.
 * @returns {Promise<object>} A promise that resolves to an object containing domain info or an error.
 */
async function fetchWhois(domain: string): Promise<object> {
  try {
    const data = await whoisDomain(domain);
    // The WHOIS response structure can vary, so we find the first available record
    const firstRecordKey = Object.keys(data)[0];
    const expiryDateStr = data[firstRecordKey]?.["Expiry Date"];

    if (!expiryDateStr) {
      // Handle cases where expiry date is not found in the WHOIS record
      return { domain, error: "Expiry Date not found in WHOIS record." };
    }

    const expiryDate = new Date(expiryDateStr);
    const now = new Date();
    const isExpired = expiryDate < now;

    return { domain, expiryDate: expiryDate.toISOString(), isExpired };
  } catch (error) {
    // Return an error object if the whois lookup fails for any reason
    return {
      domain,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Main function to orchestrate the domain fetching, processing, and filtering.
 */
async function main() {
  console.log("Starting domain processing...");

  // 1. Fetch all domain lists from sources in parallel
  console.log("Fetching domains from all sources...");
  const allLists = await Promise.all(SOURCES.map(fetchDomainsFromSource));

  // 2. Flatten, deduplicate using a Set, and apply pre-filter
  const allDomains = Array.from(new Set(allLists.flat()));
  const filteredDomains = allDomains.filter(
    (domain) => !PRE_FILTERED_DOMAINS.includes(domain)
  );
  console.log(
    `Found ${allDomains.length} unique domains. Processing ${filteredDomains.length} after pre-filtering.`
  );

  let processedCount = 0;
  const totalDomains = filteredDomains.length;

  // 3. Add all WHOIS fetch tasks to the queue
  console.log("Queueing WHOIS lookups...");
  const startTime = performance.now();
  const whoisPromises = filteredDomains.map((domain) =>
    queue.add(async () => {
      const result = await fetchWhois(domain);
      processedCount++;
      // Move cursor to start of line and update counter
      process.stdout.write(`\rProcessed: ${processedCount}/${totalDomains}`);
      return result;
    })
  );

  // 4. Wait for all queued promises to resolve
  const results = await Promise.all(whoisPromises);
  console.log("\nAll WHOIS lookups completed.");
  const endTime = performance.now();
  console.log(`Time taken: ${(endTime - startTime) / 1000} seconds`);

  // 5. Process results, separating them into active, expired, and error lists
  const activeDomains: Record<string, any>[] = [];
  const expiredDomains: Record<string, any>[] = [];
  const errorDomains: Record<string, any>[] = [];

  for (const result of results) {
    if ("error" in result) {
      errorDomains.push(result);
    } else if ("isExpired" in result && result.isExpired) {
      expiredDomains.push(result);
    } else if ("isExpired" in result && !result.isExpired) {
      activeDomains.push(result);
    }
  }

  console.log("---");
  console.log(`Processing complete!`);
  console.log(`- Active Domains: ${activeDomains.length}`);
  console.log(`- Expired Domains: ${expiredDomains.length}`);
  console.log(`- Errored Domains: ${errorDomains.length}`);
  console.log("---");

  // 6. Write the categorized lists to JSON files
  console.log("Writing results to files...");

  const activeDomainsJson = activeDomains.map((domain) => domain.domain);
  await fs.writeFile(
    "active_domains.json",
    JSON.stringify(activeDomainsJson, null, 2)
  );
  await fs.writeFile(
    "expired_domains.json",
    JSON.stringify(expiredDomains, null, 2)
  );
  await fs.writeFile(
    "error_domains.json",
    JSON.stringify(errorDomains, null, 2)
  );

  console.log(
    "Successfully created active_domains.json, expired_domains.json, and error_domains.json"
  );
}

// Run the main function and catch any top-level errors
main().catch((error) => {
  console.error("An unexpected error occurred in the main process:", error);
});
