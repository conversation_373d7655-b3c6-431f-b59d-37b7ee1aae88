import { z } from "zod";

/**
 * Validation rules schema for configuring which checks to perform
 */
export const ValidationRulesSchema = z.object({
  checkDisposable: z.boolean().optional().default(true),
  checkForMxRecords: z.boolean().optional().default(true),
  checkSyntax: z.boolean().optional().default(true),
  checkRoleBasedAccounts: z.boolean().optional().default(false),
  checkPatterns: z.boolean().optional().default(true),
});

/**
 * Whitelist/Blacklist entry schema
 */
export const ListEntrySchema = z.object({
  value: z.string(),
  type: z.enum(["email", "domain", "ip"]),
});

/**
 * API request schema for validation endpoint
 */
export const ApiRequestSchema = z.object({
  email: z.string().email("Invalid email format"),
  userRules: ValidationRulesSchema.optional(),
  userWhitelist: z.array(ListEntrySchema).optional().default([]),
  userBlacklist: z.array(ListEntrySchema).optional().default([]),
});

/**
 * Individual validation check result
 */
export const ValidationCheckResultSchema = z.object({
  name: z.string(),
  passed: z.boolean(),
  reason: z.string().optional(),
  data: z.any().optional(),
});

/**
 * Third-party checker result (internal use only)
 */
export interface ThirdPartyResult {
  input: string;
  source: string;
  valid: boolean;
  result: any;
  error?: any;
}

/**
 * Complete validation result schema (public API)
 */
export const ValidationResultSchema = z.object({
  isValid: z.boolean(),
  email: z.string(),
  reason: z.string(),
  checks: z.object({
    isWhitelisted: z.boolean().nullable(),
    isBlacklisted: z.boolean().nullable(),
    isDisposable: z.boolean().nullable(),
    hasMxRecords: z.boolean().nullable(),
    syntaxValid: z.boolean().nullable(),
    isRoleBased: z.boolean().nullable(),
    riskScore: z.number().min(0).max(100),
  }),
  reasons: z.array(z.string()),
});

/**
 * Internal validation result with third-party data (for database enrichment)
 */
export interface InternalValidationResult extends ValidationResult {
  internalData?: {
    thirdPartyResults: ThirdPartyResult[];
    enrichmentTimestamp: string;
  };
}

/**
 * Type exports
 */
export type ValidationRules = z.infer<typeof ValidationRulesSchema>;
export type ListEntry = z.infer<typeof ListEntrySchema>;
export type ApiRequest = z.infer<typeof ApiRequestSchema>;
export type ValidationCheckResult = z.infer<typeof ValidationCheckResultSchema>;
export type ValidationResult = z.infer<typeof ValidationResultSchema>;

/**
 * Configuration interface
 */
export interface ValidatorConfig {
  defaultRules: ValidationRules;
  defaultBlacklist: string[];
  defaultWhitelist: string[];
  trustedDomains: string[];
  disposableDomains: string[];
  roleBasedPrefixes: string[];
  suspiciousPatterns: RegExp[];
  thirdPartyApiKeys: {
    [key: string]: string;
  };
  // Internal settings
  enableInternalEnrichment: boolean;
  internalThirdPartyCheckers: string[];
}

/**
 * Validation context passed to individual checks
 */
export interface ValidationContext {
  email: string;
  domain: string;
  localPart: string;
  rules: ValidationRules;
  whitelist: ListEntry[];
  blacklist: ListEntry[];
  config: ValidatorConfig;
}
