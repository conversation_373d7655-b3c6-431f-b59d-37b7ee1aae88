import extractDomain from 'extract-domain';
import { loadConfig } from '../lib/loadConfig';
import { checkUserBlacklist } from './checks/checkBlacklist';
import { checkDisposable } from './checks/checkDisposable';
import { checkMxRecords } from './checks/checkMxRecords';
import { checkPatterns } from './checks/checkPatterns';
import { checkRoleBased } from './checks/checkRoleBased';
import { checkSyntax } from './checks/checkSyntax';
import { checkTrustedDomains } from './checks/checkTrustedDomains';
import { checkWhitelist } from './checks/checkWhitelist';
import { runThirdPartyCheckers } from './checks/thirdParty';
import {
  ApiRequest,
  InternalValidationResult,
  ThirdPartyResult,
  ValidationCheckResult,
  ValidationContext,
  ValidationResult,
} from './types';

/**
 * Calculate risk score based on validation results
 * @param checkResults - Array of validation check results
 * @param thirdPartyResults - Array of third-party validation results (for internal scoring)
 * @returns number - Risk score between 0-100
 */
function calculateRiskScore(
  checkResults: ValidationCheckResult[],
  thirdPartyResults: ThirdPartyResult[] = []
): number {
  let score = 0;

  for (const result of checkResults) {
    switch (result.name) {
      case 'syntax':
        if (!result.passed) score += 50;
        break;
      case 'whitelist':
        if (result.passed) score -= 30; // Lower risk for user whitelisted
        break;
      case 'trusted-domain':
        if (result.passed) score -= 20; // Lower risk for trusted domains
        break;
      case 'user_blacklist':
      case 'default_blacklist':
        if (!result.passed) score += 100;
        break;
      case 'disposable':
        if (!result.passed) score += 100;
        break;
      case 'mx-records':
        if (!result.passed) score += 25;
        break;
      case 'role-based':
        if (!result.passed) score += 15;
        break;
      case 'patterns':
        if (!result.passed) score += 20;
        break;
    }
  }

  // Factor in third-party results for internal risk assessment
  if (thirdPartyResults.length > 0) {
    const validThirdPartyChecks = thirdPartyResults.filter(
      (r) => r.valid
    ).length;
    const totalThirdPartyChecks = thirdPartyResults.length;

    // If more than half of third-party services say invalid, increase risk
    if (validThirdPartyChecks / totalThirdPartyChecks < 0.5) {
      score += 15;
    } else if (validThirdPartyChecks === totalThirdPartyChecks) {
      // All third-party services confirm valid, lower risk
      score -= 10;
    }
  }

  // Ensure score is between 0 and 100
  return Math.max(0, Math.min(100, score));
}

/**
 * Generate reasons array from validation results
 * @param checkResults - Array of validation check results
 * @returns string[] - Array of reason codes
 */
function generateReasons(checkResults: ValidationCheckResult[]): string[] {
  const reasons: string[] = [];

  for (const result of checkResults) {
    if (result.name === 'user_whitelist_domain' && result.passed) {
      reasons.push('user_whitelisted_domain'); // Only for user whitelist
    } else if (result.name === 'user_whitelist_email' && result.passed) {
      reasons.push('user_whitelisted_email');
    } else if (result.name === 'user_blacklist_domain' && !result.passed) {
      reasons.push('user_blacklisted_domain');
    } else if (result.name === 'user_blacklist_email' && !result.passed) {
      reasons.push('user_blacklisted_email');
    } else if (result.name === 'disposable' && !result.passed) {
      reasons.push('disposable_email_service');
    } else if (result.name === 'mx-records' && !result.passed) {
      reasons.push('mx_records_not_found');
    } else if (result.name === 'syntax' && !result.passed) {
      reasons.push('invalid_syntax');
    } else if (result.name === 'role-based' && !result.passed) {
      reasons.push('role_based_account');
    } else if (result.name === 'patterns' && !result.passed) {
      reasons.push('suspicious_pattern');
    }
    // Note: trusted-domain check doesn't add to reasons, only affects risk score
  }

  return reasons;
}

/**
 * Main email validation function
 * @param request - API request containing email and validation rules
 * @param env - Environment variables (for Cloudflare Workers)
 * @returns Promise<ValidationResult | InternalValidationResult> - Complete validation result
 */
export async function validateEmail(
  request: ApiRequest,
  env: any = {}
): Promise<ValidationResult | InternalValidationResult> {
  const {
    email,
    userRules = {},
    userWhitelist = [],
    userBlacklist = [],
  } = request;

  // Load configuration with environment variables
  const config = loadConfig(env);

  // Extract domain and local part
  const domain = String(extractDomain(email) || '');
  const localPart = email.split('@')[0];

  // Merge user rules with defaults
  const mergedRules = { ...config.defaultRules, ...userRules };

  // Create validation context
  const context: ValidationContext = {
    email,
    domain,
    localPart,
    rules: mergedRules,
    whitelist: userWhitelist,
    blacklist: userBlacklist,
    config,
  };

  const checkResults: ValidationCheckResult[] = [];
  let thirdPartyResults: ThirdPartyResult[] = [];
  let isValid = true;
  let reason = 'Email passed all validation checks';

  try {
    // 1. Check syntax first (always run)
    if (mergedRules.checkSyntax) {
      const syntaxResult = await checkSyntax(context);
      checkResults.push(syntaxResult);

      if (!syntaxResult.passed) {
        isValid = false;
        reason = syntaxResult.reason || 'Invalid email syntax';
        // If syntax fails, no point in continuing other checks
        const riskScore = calculateRiskScore(checkResults, thirdPartyResults);
        const reasons = generateReasons(checkResults);

        return {
          isValid,
          email,
          reason,
          checks: {
            isWhitelisted: null,
            isBlacklisted: null,
            isDisposable: null,
            hasMxRecords: null,
            syntaxValid: syntaxResult.passed,
            isRoleBased: null,
            riskScore,
          },
          reasons,
        };
      }
    }

    // 2. Check USER whitelist (if whitelisted, skip all other checks)
    const whitelistResult = await checkWhitelist(context);
    checkResults.push(whitelistResult);

    if (whitelistResult.passed) {
      isValid = true;
      reason = whitelistResult.reason || 'Email is whitelisted';
      const riskScore = calculateRiskScore(checkResults, thirdPartyResults);
      const reasons = generateReasons(checkResults);

      return {
        isValid,
        email,
        reason,
        checks: {
          isWhitelisted: true,
          isBlacklisted: null,
          isDisposable: null,
          hasMxRecords: null,
          syntaxValid:
            checkResults.find((r) => r.name === 'syntax')?.passed || null,
          isRoleBased: null,
          riskScore,
        },
        reasons,
      };
    }

    // 3. Check USER blacklist (if blacklisted, return early)
    const userBlacklistResult = await checkUserBlacklist(context);
    checkResults.push(userBlacklistResult);

    if (!userBlacklistResult.passed) {
      isValid = false;
      reason = userBlacklistResult.reason || 'Email is blacklisted by user';
      const riskScore = calculateRiskScore(checkResults, thirdPartyResults);
      const reasons = generateReasons(checkResults);

      return {
        isValid,
        email,
        reason,
        checks: {
          isWhitelisted: false,
          isBlacklisted: true,
          isDisposable: null,
          hasMxRecords: null,
          syntaxValid:
            checkResults.find((r) => r.name === 'syntax')?.passed || null,
          isRoleBased: null,
          riskScore,
        },
        reasons,
      };
    }

    // 4. Check trusted(default) domains (for risk scoring, doesn't skip other checks)
    const trustedDomainResult = await checkTrustedDomains(context);
    checkResults.push(trustedDomainResult);

    // 5. Check disposable email (always run if enabled for completeness)
    if (mergedRules.checkDisposable) {
      const disposableResult = await checkDisposable(context);
      checkResults.push(disposableResult);

      if (!disposableResult.passed) {
        isValid = false;
        reason = disposableResult.reason || 'Disposable email detected';
      }
    }

    // 7. Check MX records (CRITICAL - always run if enabled, MX failure = invalid email)
    if (mergedRules.checkForMxRecords) {
      const mxResult = await checkMxRecords(context);
      checkResults.push(mxResult);

      if (!mxResult.passed) {
        isValid = false;
        reason = mxResult.reason || 'No valid MX records found';
      }
    }

    // 8. Check role-based accounts (optional check)
    if (mergedRules.checkRoleBasedAccounts) {
      const roleResult = await checkRoleBased(context);
      checkResults.push(roleResult);

      if (!roleResult.passed && isValid) {
        isValid = false;
        reason = roleResult.reason || 'Role-based account detected';
      }
    }

    // 9. Check suspicious patterns (optional check)
    if (mergedRules.checkPatterns) {
      const patternsResult = await checkPatterns(context);
      checkResults.push(patternsResult);

      if (!patternsResult.passed && isValid) {
        isValid = false;
        reason = patternsResult.reason || 'Suspicious pattern detected';
      }
    }

    // 10. Run third-party checkers for internal enrichment (not exposed in public API)
    if (
      config.enableInternalEnrichment &&
      config.internalThirdPartyCheckers.length > 0
    ) {
      thirdPartyResults = await runThirdPartyCheckers(
        email,
        config.internalThirdPartyCheckers,
        config.thirdPartyApiKeys
      );
    }

    // Calculate final risk score and reasons
    const riskScore = calculateRiskScore(checkResults, thirdPartyResults);
    const reasons = generateReasons(checkResults);

    if (isValid && reason === 'Email passed all validation checks') {
      reason = 'Email passed all validation checks';
    }

    // Public API response (no third-party data exposed)
    const publicResult: ValidationResult = {
      isValid,
      email,
      reason,
      checks: {
        isWhitelisted:
          checkResults.find((r) => r.name === 'whitelist')?.passed || false,
        isBlacklisted:
          !checkResults.find((r) => r.name === 'user_blacklist')?.passed ||
          !checkResults.find((r) => r.name === 'default_blacklist')?.passed ||
          false,
        isDisposable: checkResults.find((r) => r.name === 'disposable')
          ? !checkResults.find((r) => r.name === 'disposable')?.passed
          : null,
        hasMxRecords:
          checkResults.find((r) => r.name === 'mx-records')?.passed || null,
        syntaxValid:
          checkResults.find((r) => r.name === 'syntax')?.passed || null,
        isRoleBased: checkResults.find((r) => r.name === 'role-based')
          ? !checkResults.find((r) => r.name === 'role-based')?.passed
          : null,
        riskScore,
      },
      reasons,
    };

    // Add internal data for database enrichment if third-party checks were run
    if (thirdPartyResults.length > 0) {
      const internalResult: InternalValidationResult = {
        ...publicResult,
        internalData: {
          thirdPartyResults,
          enrichmentTimestamp: new Date().toISOString(),
        },
      };
      return internalResult;
    }

    return publicResult;
  } catch (error) {
    const riskScore = 100;
    return {
      isValid: false,
      email,
      reason: `Validation error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      checks: {
        isWhitelisted: null,
        isBlacklisted: null,
        isDisposable: null,
        hasMxRecords: null,
        syntaxValid: null,
        isRoleBased: null,
        riskScore,
      },
      reasons: ['validation_error'],
    };
  }
}
