import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Checks if email or domain is in USER whitelist (skips all other checks)
 * Note: This is separate from trusted domains check
 * @param context - Validation context containing email and whitelist
 * @returns Promise<ValidationCheckResult> - Result of whitelist validation
 */
export async function checkWhitelist(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { email, domain, whitelist } = context;

  try {
    // Only check user-provided whitelist (not trusted domains)
    const userWhitelist = whitelist;

    // Check if email exactly matches any whitelisted email
    const emailMatch = userWhitelist.find(
      (entry) =>
        entry.type === "email" &&
        entry.value.toLowerCase() === email.toLowerCase()
    );

    if (emailMatch) {
      return {
        name: "user_whitelist_email",
        passed: true,
        reason: "Email is explicitly whitelisted by user",
        data: { matchedEntry: emailMatch },
      };
    }

    // Check if domain matches any whitelisted domain
    const domainMatch = userWhitelist.find(
      (entry) =>
        entry.type === "domain" &&
        domain.toLowerCase() === entry.value.toLowerCase()
    );

    if (domainMatch) {
      return {
        name: "user_whitelist_domain",
        passed: true,
        reason: "Domain is whitelisted",
        data: { matchedEntry: domainMatch },
      };
    }

    return {
      name: "user_whitelist_email",
      passed: false,
      reason: "Not found in whitelist",
      data: { checkedDomain: domain },
    };
  } catch (error) {
    return {
      name: "user_whitelist_email",
      passed: false,
      reason: `Whitelist check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { email, domain, error },
    };
  }
}
