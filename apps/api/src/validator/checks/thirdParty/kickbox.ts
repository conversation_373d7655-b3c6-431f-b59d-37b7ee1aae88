import { ThirdPartyResult } from "../../types";

/**
 * Kickbox.org disposable email checker (using their open API)
 * @param email - Email address to validate
 * @param apiKey - Not required for the open API
 * @returns Promise<ThirdPartyResult> - Result from Kickbox API
 */
export async function kickboxCheck(
  email: string,
  apiKey?: string
): Promise<ThirdPartyResult> {
  try {
    const response = await fetch(
      `https://open.kickbox.com/v1/disposable/${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "kickbox.org",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // Kickbox returns valid if disposable === false
    const isValid = result?.disposable === false;

    return {
      input: email,
      source: "kickbox.org",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "kickbox.org",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
