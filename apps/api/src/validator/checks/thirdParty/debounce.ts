import { ThirdPartyResult } from "../../types";

/**
 * Debounce.io disposable email checker
 * @param email - Email address to validate
 * @param apiKey - Not required for this service
 * @returns Promise<ThirdPartyResult> - Result from Debounce.io API
 */
export async function debounceCheck(
  email: string,
  apiKey?: string
): Promise<ThirdPartyResult> {
  try {
    const response = await fetch(
      `https://disposable.debounce.io/?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "debounce.io",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();
    console.log("debounceCheck result", result);

    // Debounce returns valid if disposable === "false"
    const isValid = result?.disposable === "false";

    return {
      input: email,
      source: "debounce.io",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "debounce.io",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
