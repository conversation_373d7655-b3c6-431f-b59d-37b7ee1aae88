import { ThirdPartyResult } from "../../types";
import { abuseipdbCheck } from "./abuseipdb";
import { debounceCheck } from "./debounce";
import { emailableCheck } from "./emailable";
import { ipQualityScoreCheck } from "./ipqualityscore";
import { kickboxCheck } from "./kickbox";
import { mailboxlayerCheck } from "./mailboxlayer";
import { quickEmailVerificationCheck } from "./quickemailverification";
import { stopForumSpamCheck } from "./stopforumspam";

/**
 * Available third-party checkers
 */
export const THIRD_PARTY_CHECKERS = {
  "emailable.com": emailableCheck,
  "mailboxlayer.com": mailboxlayerCheck,
  "quickemailverification.com": quickEmailVerificationCheck,
  "debounce.io": debounceCheck,
  "kickbox.org": kickboxCheck,
  "stopforumspam.org": stopForumSpamCheck,
  "ipqualityscore.com": ipQualityScoreCheck,
  "abuseipdb.com": abuseipdbCheck,
} as const;

export type ThirdPartyCheckerName = keyof typeof THIRD_PARTY_CHECKERS;

/**
 * Run selected third-party checkers
 * @param email - Email to validate
 * @param checkerNames - Names of checkers to run
 * @param apiKeys - API keys for the services
 * @returns Promise<ThirdPartyResult[]> - Results from all checkers
 */
export async function runThirdPartyCheckers(
  email: string,
  checkerNames: string[],
  apiKeys: { [key: string]: string }
): Promise<ThirdPartyResult[]> {
  const results: ThirdPartyResult[] = [];

  // Run all checkers in parallel for better performance
  const promises = checkerNames
    .filter((checkerName) => checkerName in THIRD_PARTY_CHECKERS)
    .map(async (checkerName) => {
      const checker =
        THIRD_PARTY_CHECKERS[checkerName as ThirdPartyCheckerName];
      const apiKey = apiKeys[checkerName] || "";

      try {
        return await checker(email, apiKey);
      } catch (error) {
        return {
          input: email,
          source: checkerName,
          valid: false,
          result: null,
          error: error instanceof Error ? error.message : "Unknown error",
        } as ThirdPartyResult;
      }
    });

  const checkerResults = await Promise.allSettled(promises);

  for (const result of checkerResults) {
    if (result.status === "fulfilled") {
      results.push(result.value);
    }
  }

  console.log("checkerResults", JSON.stringify(results, null, 2));

  return results;
}

// Export individual checkers
export {
  abuseipdbCheck,
  debounceCheck,
  emailableCheck,
  ipQualityScoreCheck,
  kickboxCheck,
  mailboxlayerCheck,
  quickEmailVerificationCheck,
  stopForumSpamCheck,
};
