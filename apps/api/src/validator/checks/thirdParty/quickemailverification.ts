import { ThirdPartyResult } from "../../types";

/**
 * QuickEmailVerification.com API checker
 * @param email - Email address to validate
 * @param apiKey - QuickEmailVerification API key
 * @returns Promise<ThirdPartyResult> - Result from QuickEmailVerification API
 */
export async function quickEmailVerificationCheck(
  email: string,
  apiKey: string
): Promise<ThirdPartyResult> {
  try {
    if (!apiKey) {
      return {
        input: email,
        source: "quickemailverification.com",
        valid: false,
        result: null,
        error: "API key not provided",
      };
    }

    const response = await fetch(
      `http://api.quickemailverification.com/v1/verify?apikey=${apiKey}&email=${encodeURIComponent(
        email
      )}&nocache=1`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "quickemailverification.com",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // QuickEmailVerification: result === "valid"
    const isValid = result?.result === "valid";

    return {
      input: email,
      source: "quickemailverification.com",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "quickemailverification.com",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
