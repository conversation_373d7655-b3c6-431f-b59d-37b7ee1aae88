import { ThirdPartyResult } from "../../types";

/**
 * IPQualityScore.com email verification API checker
 * @param email - Email address to validate
 * @param apiKey - IPQualityScore API key
 * @returns Promise<ThirdPartyResult> - Result from IPQualityScore API
 */
export async function ipQualityScoreCheck(
  email: string,
  apiKey: string
): Promise<ThirdPartyResult> {
  try {
    if (!apiKey) {
      return {
        input: email,
        source: "ipqualityscore.com",
        valid: false,
        result: null,
        error: "API key not provided",
      };
    }

    const response = await fetch(
      `https://www.ipqualityscore.com/api/json/email/${apiKey}/${encodeURIComponent(
        email
      )}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "ipqualityscore.com",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // IPQualityScore returns valid directly in the 'valid' field
    const isValid = result?.valid === true;

    return {
      input: email,
      source: "ipqualityscore.com",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "ipqualityscore.com",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
