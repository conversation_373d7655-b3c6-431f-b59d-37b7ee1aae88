import { ThirdPartyResult } from "../../types";

/**
 * StopForumSpam.org email spam checker
 * @param email - Email address to validate
 * @param apiKey - Not required for this service
 * @returns Promise<ThirdPartyResult> - Result from StopForumSpam API
 */
export async function stopForumSpamCheck(
  email: string,
  apiKey?: string
): Promise<ThirdPartyResult> {
  try {
    const response = await fetch(
      `https://api.stopforumspam.org/api?email=${encodeURIComponent(
        email
      )}&json`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "stopforumspam.org",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // StopForumSpam returns valid if email does NOT appear in their spam database
    // Check both 'appears' and 'blacklisted' flags
    const isValid = !(
      result?.email?.appears === 1 || result?.email?.blacklisted === 1
    );

    return {
      input: email,
      source: "stopforumspam.org",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "stopforumspam.org",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
