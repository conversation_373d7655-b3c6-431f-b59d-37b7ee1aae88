import { ThirdPartyResult } from "../../types";

/**
 * Emailable.com email verification API checker
 * @param email - Email address to validate
 * @param apiKey - Emailable API key
 * @returns Promise<ThirdPartyResult> - Result from Emailable API
 */
export async function emailableCheck(
  email: string,
  apiKey: string
): Promise<ThirdPartyResult> {
  try {
    if (!apiKey) {
      return {
        input: email,
        source: "emailable.com",
        valid: false,
        result: null,
        error: "API key not provided",
      };
    }

    const response = await fetch(
      `https://api.emailable.com/v1/verify?email=${encodeURIComponent(
        email
      )}&api_key=${apiKey}`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "emailable.com",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // Emailable scoring: score >= 70 and not disposable = valid
    const isValid = result?.score >= 70 && result?.disposable === false;

    return {
      input: email,
      source: "emailable.com",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "emailable.com",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
