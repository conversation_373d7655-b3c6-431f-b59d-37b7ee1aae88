import { ThirdPartyResult } from "../../types";

/**
 * Simple IP validation helper
 */
function isValidIP(ip: string): boolean {
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * Check if IP is a local/private IP
 */
function isLocalIP(ip: string): boolean {
  if (!isValidIP(ip)) return false;

  const parts = ip.split(".").map(Number);
  if (parts.length !== 4) return false;

  // Check for private IP ranges
  return (
    parts[0] === 10 ||
    (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
    (parts[0] === 192 && parts[1] === 168) ||
    parts[0] === 127 // localhost
  );
}

/**
 * Resolve domain to IP address using DNS over HTTPS
 */
async function getDomainIP(domain: string): Promise<string | null> {
  try {
    const response = await fetch(
      `https://cloudflare-dns.com/dns-query?name=${domain}&type=A`,
      {
        headers: { Accept: "application/dns-json" },
      }
    );

    if (!response.ok) return null;

    const data = await response.json();
    const aRecords =
      data.Answer?.filter((record: any) => record.type === 1) || [];

    return aRecords.length > 0 ? aRecords[0].data : null;
  } catch (error) {
    return null;
  }
}

/**
 * AbuseIPDB.com IP reputation checker for email domains
 * @param email - Email address to validate (domain IP will be checked)
 * @param apiKey - AbuseIPDB API key
 * @returns Promise<ThirdPartyResult> - Result from AbuseIPDB API
 */
export async function abuseipdbCheck(
  email: string,
  apiKey: string
): Promise<ThirdPartyResult> {
  try {
    if (!apiKey) {
      return {
        input: email,
        source: "abuseipdb.com",
        valid: false,
        result: null,
        error: "API key not provided",
      };
    }

    let domainIP: string | null;

    // Check if the email domain part is already an IP
    const domain = email.split("@")[1] || email;

    if (isValidIP(domain)) {
      domainIP = domain;
    } else {
      // Resolve domain to IP
      domainIP = await getDomainIP(domain);
    }

    if (!domainIP) {
      return {
        input: email,
        source: "abuseipdb.com",
        valid: false,
        result: null,
        error: "Could not resolve domain IP address",
      };
    }

    if (isLocalIP(domainIP)) {
      return {
        input: email,
        source: "abuseipdb.com",
        valid: false,
        result: null,
        error: "Domain resolves to local/private IP address",
      };
    }

    const response = await fetch(
      `https://api.abuseipdb.com/api/v2/check?maxAgeInDays=90&verbose&ipAddress=${domainIP}`,
      {
        method: "GET",
        headers: {
          Key: apiKey,
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "abuseipdb.com",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // AbuseIPDB returns valid if there are NO total reports for the IP
    const isValid =
      !result?.data?.totalReports || result.data.totalReports === 0;

    return {
      input: email,
      source: "abuseipdb.com",
      valid: isValid,
      result: {
        ...result,
        checkedIP: domainIP,
        domain: domain,
      },
    };
  } catch (error) {
    return {
      input: email,
      source: "abuseipdb.com",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
