import { ThirdPartyResult } from "../../types";

/**
 * Mailboxlayer.com email verification API checker
 * @param email - Email address to validate
 * @param apiKey - Mailboxlayer access key
 * @returns Promise<ThirdPartyResult> - Result from Mailboxlayer API
 */
export async function mailboxlayerCheck(
  email: string,
  apiKey: string
): Promise<ThirdPartyResult> {
  try {
    if (!apiKey) {
      return {
        input: email,
        source: "mailboxlayer.com",
        valid: false,
        result: null,
        error: "API key not provided",
      };
    }

    const response = await fetch(
      `http://apilayer.net/api/check?access_key=${apiKey}&email=${encodeURIComponent(
        email
      )}&smtp=1&format=1`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
      }
    );

    if (!response.ok) {
      return {
        input: email,
        source: "mailboxlayer.com",
        valid: false,
        result: null,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    const result = await response.json();

    // Mailboxlayer validation: format_valid && !disposable && mx_found
    const isValid =
      result?.format_valid &&
      result?.disposable === false &&
      result?.mx_found === true;

    return {
      input: email,
      source: "mailboxlayer.com",
      valid: isValid,
      result,
    };
  } catch (error) {
    return {
      input: email,
      source: "mailboxlayer.com",
      valid: false,
      result: null,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
