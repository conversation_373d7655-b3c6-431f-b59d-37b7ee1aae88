import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Checks email against suspicious patterns
 * @param context - Validation context containing email and configuration
 * @returns Promise<ValidationCheckResult> - Result of pattern validation
 */
export async function checkPatterns(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { email, config } = context;

  try {
    const matchedPatterns: { pattern: RegExp; description: string }[] = [];

    // Check against each suspicious pattern
    for (const pattern of config.suspiciousPatterns) {
      if (pattern.test(email.toLowerCase())) {
        matchedPatterns.push({
          pattern,
          description: `Matched suspicious pattern: ${pattern.source}`,
        });
      }
    }

    if (matchedPatterns.length > 0) {
      return {
        name: "patterns",
        passed: false,
        reason: `Email matches ${matchedPatterns.length} suspicious pattern(s)`,
        data: {
          email,
          matchedPatterns: matchedPatterns.map((p) => p.description),
        },
      };
    }

    return {
      name: "patterns",
      passed: true,
      reason: "Email does not match any suspicious patterns",
      data: { email },
    };
  } catch (error) {
    return {
      name: "patterns",
      passed: false,
      reason: `Pattern check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { email, error },
    };
  }
}
