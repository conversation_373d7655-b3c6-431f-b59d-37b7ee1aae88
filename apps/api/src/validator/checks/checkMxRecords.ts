import { promises as dns } from "node:dns";
import { ValidationCheckResult, ValidationContext } from "../types";

interface MXRecord {
  priority: number;
  exchange: string;
}

/**
 * Checks if domain has valid MX records for email delivery using Node.js dns
 * @param context - Validation context containing email and configuration
 * @returns Promise<ValidationCheckResult> - Result of MX record validation
 */
export async function checkMxRecords(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { domain } = context;

  try {
    // Use Node.js dns.promises.resolveMx
    const mxRecords = await dns.resolveMx(domain);

    if (!mxRecords || mxRecords.length === 0) {
      return {
        name: "mx-records",
        passed: false,
        reason: "No MX records found for domain",
        data: { domain },
      };
    }

    // Filter and validate MX records
    const validMxRecords: MXRecord[] = mxRecords
      .filter(
        (record: any) =>
          typeof record.priority === "number" &&
          typeof record.exchange === "string" &&
          record.exchange !== "."
      )
      .map((record) => ({
        priority: record.priority,
        exchange: record.exchange,
      }));

    if (validMxRecords.length === 0) {
      return {
        name: "mx-records",
        passed: false,
        reason: "MX records found but no valid mail exchanges",
        data: { domain, mxRecords },
      };
    }

    return {
      name: "mx-records",
      passed: true,
      reason: `Found ${validMxRecords.length} valid MX record(s)`,
      data: {
        domain,
        mxRecords: validMxRecords,
      },
    };
  } catch (error) {
    return {
      name: "mx-records",
      passed: false,
      reason: `MX record check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { domain, error },
    };
  }
}
