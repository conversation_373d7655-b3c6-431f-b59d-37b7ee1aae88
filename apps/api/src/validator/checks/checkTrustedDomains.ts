import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Checks if domain is a trusted domain (major email providers)
 * This check doesn't fail validation, it's used for risk scoring
 * @param context - Validation context containing email and domain
 * @returns Promise<ValidationCheckResult> - Result of trusted domain check
 */
export async function checkTrustedDomains(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { domain, config } = context;

  try {
    // TODO: need to check the subdomain as well
    // Check if domain is in trusted domains list
    const isTrusted = config.trustedDomains.some(
      (trustedDomain) => domain.toLowerCase() === trustedDomain.toLowerCase()
    );

    return {
      name: "trusted-domain",
      passed: isTrusted,
      reason: isTrusted
        ? "Domain is a trusted email provider"
        : "Domain is not a major email provider",
      data: { domain, isTrusted },
    };
  } catch (error) {
    return {
      name: "trusted-domain",
      passed: false,
      reason: `Trusted domain check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { domain, error },
    };
  }
}
