import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Checks if email domain is a known disposable/temporary email service
 * @param context - Validation context containing email and configuration
 * @returns Promise<ValidationCheckResult> - Result of disposable email validation
 */
export async function checkDisposable(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { domain, config } = context;

  try {
    // Check if domain matches any known disposable domain
    // Using .endsWith for subdomain matching (e.g., user.mailinator.com)
    const isDisposable = config.disposableDomains.some((disposableDomain) =>
      domain.toLowerCase().endsWith(disposableDomain.toLowerCase())
    );

    if (isDisposable) {
      return {
        name: "disposable",
        passed: false,
        reason: "Domain is a known disposable email service",
        data: {
          domain,
          matchedDomains: config.disposableDomains.filter((d) =>
            domain.toLowerCase().endsWith(d.toLowerCase())
          ),
        },
      };
    }

    return {
      name: "disposable",
      passed: true,
      reason: "Domain is not a known disposable email service",
      data: { domain },
    };
  } catch (error) {
    return {
      name: "disposable",
      passed: false,
      reason: `Disposable check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { domain, error },
    };
  }
}
