import { ValidationCheckResult, ValidationContext } from '../types';

/**
 * Checks if email or domain is in USER blacklist (returns early if found)
 * @param context - Validation context containing email and user blacklist
 * @returns Promise<ValidationCheckResult> - Result of user blacklist validation
 */
export async function checkUserBlacklist(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { email, domain, blacklist } = context;

  try {
    // Only check user-provided blacklist
    const userBlacklist = blacklist;

    // Check if email exactly matches any blacklisted email
    const emailMatch = userBlacklist.find(
      (entry) =>
        entry.type === 'email' &&
        entry.value.toLowerCase() === email.toLowerCase()
    );

    if (emailMatch) {
      return {
        name: 'user_blacklist_email',
        passed: false,
        reason: 'Email is explicitly blacklisted by user',
        data: { matchedEntry: emailMatch },
      };
    }

    // Check if domain matches any blacklisted domain
    const domainMatch = userBlacklist.find(
      (entry) =>
        entry.type === 'domain' &&
        domain.toLowerCase() === entry.value.toLowerCase()
    );

    if (domainMatch) {
      return {
        name: 'user_blacklist_domain',
        passed: false,
        reason: 'Domain is blacklisted by user',
        data: { matchedEntry: domainMatch },
      };
    }

    return {
      name: 'user_blacklist',
      passed: true,
      reason: 'Not found in user blacklist',
      data: { checkedDomain: domain },
    };
  } catch (error) {
    return {
      name: 'user_blacklist',
      passed: false,
      reason: `User blacklist check error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      data: { email, domain, error },
    };
  }
}

/**
 * Checks if email or domain is in DEFAULT blacklist (disposable domains, etc.)
 * @param context - Validation context containing email and default config
 * @returns Promise<ValidationCheckResult> - Result of default blacklist validation
 */
export async function checkBlacklist(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { email, domain, config } = context;

  try {
    // Only check default blacklist (disposable domains, etc.)
    const defaultBlacklist = config.defaultBlacklist.map((value) => ({
      value,
      type: 'domain' as const,
    }));

    // Check if domain matches any default blacklisted domain
    const domainMatch = defaultBlacklist.find(
      (entry) =>
        entry.type === 'domain' &&
        domain.toLowerCase() === entry.value.toLowerCase()
    );

    if (domainMatch) {
      return {
        name: 'default_blacklist',
        passed: false,
        reason: 'Domain is in default blacklist',
        data: { matchedEntry: domainMatch },
      };
    }

    return {
      name: 'default_blacklist',
      passed: true,
      reason: 'Not found in default blacklist',
      data: { checkedDomain: domain },
    };
  } catch (error) {
    return {
      name: 'default_blacklist',
      passed: false,
      reason: `Default blacklist check error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      data: { email, domain, error },
    };
  }
}
