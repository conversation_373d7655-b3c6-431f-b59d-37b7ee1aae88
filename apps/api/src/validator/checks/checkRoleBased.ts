import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Checks if email appears to be a role-based account (admin, support, etc.)
 * @param context - Validation context containing email and configuration
 * @returns Promise<ValidationCheckResult> - Result of role-based validation
 */
export async function checkRoleBased(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { localPart, config } = context;

  try {
    const localPartLower = localPart.toLowerCase();

    // Check against configured role-based prefixes
    const matchedPrefix = config.roleBasedPrefixes.find((prefix) =>
      localPartLower.startsWith(prefix.toLowerCase())
    );

    console.log("matchedPrefix", matchedPrefix);

    if (matchedPrefix) {
      return {
        name: "role-based",
        passed: false,
        reason: "Email appears to be a role-based account",
        data: { localPart, matchedPrefix },
      };
    }

    return {
      name: "role-based",
      passed: true,
      reason: "Email does not appear to be a role-based account",
      data: { localPart },
    };
  } catch (error) {
    return {
      name: "role-based",
      passed: false,
      reason: `Role-based check error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { localPart, error },
    };
  }
}
