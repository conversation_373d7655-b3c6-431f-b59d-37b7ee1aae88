import { validate } from "email-validator";
import { ValidationCheckResult, ValidationContext } from "../types";

/**
 * Validates email syntax using the email-validator library
 * @param context - Validation context containing email and configuration
 * @returns Promise<ValidationCheckResult> - Result of syntax validation
 */
export async function checkSyntax(
  context: ValidationContext
): Promise<ValidationCheckResult> {
  const { email } = context;

  try {
    const isValid = validate(email);

    return {
      name: "syntax",
      passed: isValid,
      reason: isValid ? "Email syntax is valid" : "Invalid email syntax",
      data: { email },
    };
  } catch (error) {
    return {
      name: "syntax",
      passed: false,
      reason: `Syntax validation error: ${
        error instanceof Error ? error.message : "Unknown error"
      }`,
      data: { email, error },
    };
  }
}
