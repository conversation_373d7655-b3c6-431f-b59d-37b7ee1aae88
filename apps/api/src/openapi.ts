// OpenAPI schema for Email Validator API

const openapi = {
  openapi: "3.0.3",
  info: {
    title: "Email Validator API",
    version: "1.0.0",
    description:
      "API for validating email addresses with risk scoring, pattern checks, and enrichment.",
  },
  servers: [
    { url: "http://localhost:8787", description: "Local server" },
    { url: "https://your-deployed-url.com", description: "Production server" },
  ],
  paths: {
    "/": {
      get: {
        summary: "Health Check",
        description: "Returns service status and available features.",
        responses: {
          "200": {
            description: "Service status",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    status: { type: "string", example: "ok" },
                    features: { type: "array", items: { type: "string" } },
                  },
                },
              },
            },
          },
        },
      },
    },
    "/validate": {
      post: {
        summary: "Validate Email",
        description:
          "Validates an email address and returns risk assessment and details.",
        requestBody: {
          required: true,
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  email: {
                    type: "string",
                    format: "email",
                    example: "<EMAIL>",
                  },
                  userRules: {
                    type: "object",
                    properties: {
                      checkDisposable: { type: "boolean", default: true },
                      checkForMxRecords: { type: "boolean", default: true },
                      checkSyntax: { type: "boolean", default: true },
                      checkRoleBasedAccounts: {
                        type: "boolean",
                        default: false,
                      },
                      checkPatterns: { type: "boolean", default: true },
                    },
                    required: [],
                  },
                  userWhitelist: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        value: { type: "string" },
                        type: {
                          type: "string",
                          enum: ["email", "domain", "ip"],
                        },
                      },
                      required: ["value", "type"],
                    },
                    default: [],
                  },
                  userBlacklist: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        value: { type: "string" },
                        type: {
                          type: "string",
                          enum: ["email", "domain", "ip"],
                        },
                      },
                      required: ["value", "type"],
                    },
                    default: [],
                  },
                },
                required: ["email"],
              },
            },
          },
        },
        responses: {
          "200": {
            description: "Valid email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    isValid: { type: "boolean" },
                    email: { type: "string" },
                    reason: { type: "string" },
                    checks: {
                      type: "object",
                      properties: {
                        isWhitelisted: { type: "boolean", nullable: true },
                        isBlacklisted: { type: "boolean", nullable: true },
                        isDisposable: { type: "boolean", nullable: true },
                        hasMxRecords: { type: "boolean", nullable: true },
                        syntaxValid: { type: "boolean", nullable: true },
                        isRoleBased: { type: "boolean", nullable: true },
                        riskScore: { type: "number" },
                      },
                    },
                    reasons: { type: "array", items: { type: "string" } },
                  },
                },
              },
            },
          },
          "400": {
            description: "Invalid email",
            content: {
              "application/json": {
                schema: {
                  type: "object",
                  properties: {
                    isValid: { type: "boolean" },
                    email: { type: "string" },
                    reason: { type: "string" },
                    checks: {
                      type: "object",
                      properties: {
                        isWhitelisted: { type: "boolean", nullable: true },
                        isBlacklisted: { type: "boolean", nullable: true },
                        isDisposable: { type: "boolean", nullable: true },
                        hasMxRecords: { type: "boolean", nullable: true },
                        syntaxValid: { type: "boolean", nullable: true },
                        isRoleBased: { type: "boolean", nullable: true },
                        riskScore: { type: "number" },
                      },
                    },
                    reasons: { type: "array", items: { type: "string" } },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};

export default openapi;
