import { <PERSON><PERSON><PERSON> } from "@scalar/hono-api-reference";
import { <PERSON>o } from "hono";
import { cors } from "hono/cors";
import { z } from "zod";
import openapi from "./openapi";
import { validateEmail } from "./validator";
import { ApiRequestSchema, ValidationResultSchema } from "./validator/types";

const app = new Hono();

// Add CORS middleware
app.use("*", cors());

// Global error handler
app.onError((err, c) => {
  console.error("Global error:", err);

  // Handle Zod validation errors
  if (err instanceof z.ZodError) {
    return c.json(
      {
        error: "Validation failed",
        details: err.errors,
      },
      422
    );
  }

  return c.json(
    {
      error: "Internal Server Error",
      message: err.message || "Unknown error occurred",
    },
    500
  );
});

/**
 * Health check endpoint
 * GET /
 */
app.get("/", (c) => {
  console.log(c.env);
  return c.json({
    service: "Email Validator",
    version: "1.0.0",
    status: "healthy",
    timestamp: new Date().toISOString(),
    features: [
      "Syntax validation",
      "MX record verification",
      "Disposable email detection",
      "Domain whitelist/blacklist",
      "Role-based account detection",
      "Suspicious pattern matching",
      "Risk scoring (0-100)",
    ],
    endpoints: {
      validate: "POST /validate",
      health: "GET /",
    },
  });
});

/**
 * Email validation endpoint
 * POST /validate
 */
app.post("/validate", async (c) => {
  try {
    // Parse and validate request body
    const body = await c.req.json();
    const validatedRequest = ApiRequestSchema.parse(body);

    // Get environment from Cloudflare Workers binding
    const env = c.env;

    // Perform email validation
    const result = await validateEmail(validatedRequest, env);

    // Extract only public result (remove internal data if present)
    const publicResult = {
      isValid: result.isValid,
      email: result.email,
      reason: result.reason,
      checks: result.checks,
      reasons: result.reasons,
    };

    // Validate response matches schema
    const validatedResponse = ValidationResultSchema.parse(publicResult);

    // Return appropriate status code
    // const statusCode = validatedResponse.isValid ? 200 : 400;

    return c.json(validatedResponse, 200);
  } catch (error) {
    console.error("Validation error:", error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      return c.json(
        {
          error: "Invalid request format",
          details: error.errors,
        },
        422
      );
    }

    // Handle other errors
    return c.json(
      {
        error: "Validation failed",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      500
    );
  }
});

app.get("/openapi.json", (c) => {
  // Get the current server URL from the request
  const origin = new URL(c.req.url).origin;

  // Update the OpenAPI schema servers array
  const dynamicOpenapi = {
    ...openapi,
    servers: [{ url: origin, description: "Current server" }],
  };

  return c.json(dynamicOpenapi);
});

// Serve Scalar UI at /docs by redirecting to Scalar's hosted UI
app.get("/docs", Scalar({ url: "/openapi.json", theme: "elysiajs" }));

export default app;
