import defaultRulesJson from "../config/defaultRules.json";
import disposableDomainsJson from "../config/disposable-domains.json";
import trustedDomainsJson from "../config/trusted-domains.json";
import { ValidationRules, ValidatorConfig } from "../validator/types";

/**
 * Load and parse configuration files
 * @param env - Environment variables (for Cloudflare Workers)
 * @returns ValidatorConfig - Complete validator configuration
 */
export function loadConfig(env: any = {}): ValidatorConfig {
  // Parse rules from JSON
  const defaultRules: ValidationRules = defaultRulesJson;

  // Use imported domain lists from JSON files
  const defaultBlacklist = disposableDomainsJson;
  const trustedDomains = trustedDomainsJson;
  const defaultWhitelist: string[] = []; // User whitelist is handled separately

  // Use blacklist as disposable domains list
  const disposableDomains = defaultBlacklist;

  // Common role-based email prefixes
  const roleBasedPrefixes = [
    "admin",
    "administrator",
    "support",
    "help",
    "noreply",
    "no-reply",
    "info",
    "contact",
    "sales",
    "billing",
    "accounts",
    "accounting",
    "webmaster",
    "postmaster",
    "hostmaster",
    "abuse",
    "security",
    "privacy",
    "legal",
    "compliance",
    "marketing",
    "notifications",
    "alerts",
    "donotreply",
    "do-not-reply",
    "mailer-daemon",
    "bounce",
    "unsubscribe",
    "newsletter",
    "news",
    "updates",
    "system",
    "automated",
  ];

  // Suspicious email patterns
  const suspiciousPatterns = [
    /^\d+@/, // emails starting with numbers only
    /^[a-z]{1,3}\d+@/, // very short letters followed by numbers
    /^\w+\+.*test.*@/, // emails with +test in them
    /^\w+\+.*temp.*@/, // emails with +temp in them
    /^\w+\+.*spam.*@/, // emails with +spam in them
    /^test\d*@/, // emails starting with test
    /^temp\d*@/, // emails starting with temp
    /^demo\d*@/, // emails starting with demo
    /^fake\d*@/, // emails starting with fake
    /^spam\d*@/, // emails starting with spam
    /^\w*(?:temp|test|fake|spam|demo)\w*@/i, // containing suspicious words
  ];

  // Third-party API keys (from environment variables)
  const thirdPartyApiKeys = {
    "emailable.com": env.EMAILABLE_KEY || "",
    "mailboxlayer.com": env.MAILBOXLAYER_KEY || "",
    "quickemailverification.com": env.QUICKEMAILVERIFICATION_KEY || "",
    "debounce.io": env.DEBOUNCE_KEY || "", // Optional - uses free API
    "kickbox.org": env.KICKBOX_KEY || "", // Optional - uses open API
    "stopforumspam.org": env.STOPFORUMSPAM_KEY || "", // Optional - no key needed
    "ipqualityscore.com": env.IPQUALITYSCORE_KEY || "",
    "abuseipdb.com": env.ABUSEIPDB_KEY || "",
  };

  // Internal enrichment configuration
  const enableInternalEnrichment = env.ENABLE_INTERNAL_ENRICHMENT !== "false"; // Default to true
  const internalThirdPartyCheckers = [
    "debounce.io",
    "kickbox.org",
    "stopforumspam.org",
    // Add premium services only if API keys are available
    ...(env.EMAILABLE_KEY ? ["emailable.com"] : []),
    ...(env.MAILBOXLAYER_KEY ? ["mailboxlayer.com"] : []),
    ...(env.QUICKEMAILVERIFICATION_KEY ? ["quickemailverification.com"] : []),
    ...(env.IPQUALITYSCORE_KEY ? ["ipqualityscore.com"] : []),
    ...(env.ABUSEIPDB_KEY ? ["abuseipdb.com"] : []),
  ];

  return {
    defaultRules,
    defaultBlacklist,
    defaultWhitelist,
    trustedDomains,
    disposableDomains,
    roleBasedPrefixes,
    suspiciousPatterns,
    thirdPartyApiKeys,
    enableInternalEnrichment,
    internalThirdPartyCheckers,
  };
}
