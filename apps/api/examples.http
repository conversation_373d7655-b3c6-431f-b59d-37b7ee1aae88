# @baseUrl = http://localhost:8787
@baseUrl = https://email-validator.sfsajid91.workers.dev

###

# Health check
GET {{baseUrl}}/
Content-Type: application/json

###

# Valid email - basic validation
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

# Valid email with custom rules
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userRules": {
    "checkDisposable": true,
    "checkForMxRecords": true,
    "checkSyntax": true,
    "checkRoleBasedAccounts": false,
    "checkPatterns": true
  }
}

###

# Valid email with custom whitelist
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

# Invalid email - disposable
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>"
}

###

# Invalid email - blacklisted domain
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userBlacklist": [
    {
      "value": "spam-domain.com", 
      "type": "domain"
    }
  ]
}

###

# Invalid email - syntax error
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "invalid.email.format"
}

###

# Invalid email - role-based account  
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userRules": {
    "checkRoleBasedAccounts": true
  }
}

###

# Valid email - disable MX check
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userRules": {
    "checkForMxRecords": false
  }
}

###

# Invalid email - suspicious pattern
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userRules": {
    "checkPatterns": true
  }
}

###

# Test complete validation with all checks enabled
POST {{baseUrl}}/validate
Content-Type: application/json

{
  "email": "<EMAIL>",
  "userRules": {
    "checkDisposable": true,
    "checkForMxRecords": true,
    "checkSyntax": true, 
    "checkRoleBasedAccounts": true,
    "checkPatterns": true
  },
  "userBlacklist": [
    {
      "value": "blocked-domain.com",
      "type": "domain"
    }
  ],
  "userWhitelist": [
    {
      "value": "company.com",
      "type": "domain"
    }
  ]
}
