# Email Validator API

A modern, high-performance email validation service built with Hono and TypeScript, designed for Cloudflare Workers and Node.js environments.

## Features

- **Comprehensive Validation**: Syntax, MX records, disposable domains, role-based accounts
- **Advanced Risk Scoring**: Intelligent 0-100 risk assessment with maximum penalty for critical violations
- **Pattern Detection**: Suspicious email pattern matching
- **Configurable Rules**: Customizable validation logic per request
- **Whitelist/Blacklist**: Domain and email-level filtering
- **Third-Party Enrichment**: Optional integration with premium email validation services
- **High Performance**: Built for serverless and Node.js environments
- **Type Safe**: Full TypeScript support with Zod validation

## API Reference & Documentation

- **OpenAPI Schema:**
  - Served at [`/openapi.json`](http://localhost:8787/openapi.json) (or your deployed URL)
- **Modern API Docs (Scalar):**
  - Interactive API reference available at [`/docs`](http://localhost:8787/docs)
  - Powered by [<PERSON>alar](https://scalar.com/) with the `elysiajs` theme for a beautiful, modern UI

You can use the Scalar UI to explore endpoints, see request/response schemas, and try out the API interactively.

## Internal Data Enrichment

This API includes optional third-party validation services for database enrichment and enhanced accuracy. These services can be used for:

- **Real-time Enhancement**: Improve validation accuracy for critical user flows
- **Background Enrichment**: Asynchronously enrich your database with detailed email intelligence
- **Risk Assessment**: Aggregate multiple sources for comprehensive risk scoring

### Supported Services

- **Free Services**: Debounce.io, Kickbox.org, StopForumSpam.org
- **Premium Services**: Emailable.com, Mailboxlayer.com, QuickEmailVerification.com, IPQualityScore.com, AbuseIPDB.com

### Usage Patterns

- **Fallback Mode**: Use third-party APIs only when internal checks are inconclusive
- **Enrichment Mode**: Run background workers to enrich database with detailed validation data
- **Cached Results**: Store and reuse API results to minimize costs and improve performance

## Quick Start

### Prerequisites

- Node.js 18+
- pnpm
- Cloudflare Workers account (for deployment)

### Installation

```bash
# Clone and setup
cd email-validator
pnpm install

# Development
pnpm dev

# Deploy to Cloudflare Workers
pnpm deploy
```

### Environment Setup

Copy the provided `example.dev.vars` to `.dev.vars` and fill in your own API keys as needed:

```bash
cp example.dev.vars .dev.vars
# Edit .dev.vars to add your API keys
```

These variables control internal enrichment and premium third-party integrations.

Create a `wrangler.toml` file with your environment variables for deployment:

```toml
name = "email-validator"
main = "src/index.ts"
compatibility_date = "2024-01-15"

[env.production.vars]
# Internal enrichment (optional - for premium services)
EMAILABLE_KEY = "your_emailable_key"
MAILBOXLAYER_KEY = "your_mailboxlayer_key"
QUICKEMAILVERIFICATION_KEY = "your_quickemailverification_key"
IPQUALITYSCORE_KEY = "your_ipqualityscore_key"
ABUSEIPDB_KEY = "your_abuseipdb_key"

# Control internal enrichment (default: true)
ENABLE_INTERNAL_ENRICHMENT = "true"
```

## Validation Process

The validation follows this order (stops on first failure):

1. **Syntax Check** - Validates email format
2. **Whitelist Check** - If whitelisted, immediately returns valid
3. **Blacklist Check** - If blacklisted, fails validation
4. **Disposable Check** - Checks against known disposable domains
5. **MX Records Check** - Verifies domain has mail servers
6. **Role-based Check** - Detects admin/support type accounts
7. **Pattern Check** - Identifies suspicious patterns
8. **Internal Enrichment** - Runs third-party validation (background)

## Platform Deployment

### Cloudflare Workers

```bash
pnpm deploy
```

### Other Platforms

The API is platform-agnostic and can be deployed to:

- Vercel Edge Functions
- Deno Deploy
- AWS Lambda
- Any Node.js environment

## Configuration

### Default Domain Lists

- **Disposable Domains**: 400+ known temporary email services
- **Trusted Domains**: Common legitimate email providers
- **Role-based Prefixes**: Standard administrative email prefixes

### Customization

All validation rules can be overridden per request. Default configurations are stored in:

- `src/config/defaultRules.json`
- `src/config/disposable-domains.json`
- `src/config/trusted-domains.json`

## Development

### Project Structure

```
src/
├── index.ts                 # Hono app entry point
├── validator/
│   ├── index.ts            # Main validation orchestrator
│   ├── types.ts            # TypeScript types and schemas
│   └── checks/             # Individual validation modules
│       ├── checkSyntax.ts
│       ├── checkMxRecords.ts
│       ├── checkDisposable.ts
│       ├── checkWhitelist.ts
│       ├── checkBlacklist.ts
│       ├── checkRoleBased.ts
│       ├── checkPatterns.ts
│       └── thirdParty/     # Internal enrichment services
├── config/                 # Configuration files
└── lib/                    # Utility functions
```

### Adding Custom Checks

1. Create a new file in `src/validator/checks/`
2. Export a function matching the `ValidationCheckResult` interface
3. Add the check to the main validation flow in `src/validator/index.ts`

### Testing

```bash
# Run examples
pnpm dev

# Test with HTTP client
curl -X POST http://localhost:8787/validate \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## Error Handling

- **422 Unprocessable Entity**: Invalid request format
- **400 Bad Request**: Email failed validation
- **500 Internal Server Error**: Server error
- **200 OK**: Email passed validation

All errors include detailed error messages and validation details.

## Performance

- **Cold Start**: < 100ms (Cloudflare Workers)
- **Response Time**: < 50ms (cached DNS)
- **Concurrent Requests**: Scales automatically
- **Internal Enrichment**: Runs in parallel, non-blocking

## Security

- Input validation with Zod schemas
- No sensitive data logging
- API keys secured via environment variables
- Internal third-party integration (not exposed publicly)

## Technical Implementation

### MX Record Validation

Uses Node.js `dns.promises.resolveMx()` for reliable MX record checking, compatible with both:

- **Node.js environments**: Full DNS resolution capabilities
- **Cloudflare Workers**: Leverages Worker's Node.js compatibility for dns functions

### Third-Party API Integration

Designed for efficient and cost-effective usage:

```javascript
// Example: Efficient API usage pattern
async function validateWithEnrichment(email) {
  // 1. Run fast local checks first
  const localResult = await runLocalValidation(email);

  // 2. Only use third-party APIs for edge cases
  if (localResult.needsEnrichment) {
    const enrichment = await callThirdPartyAPI(email);
    return combineResults(localResult, enrichment);
  }

  return localResult;
}
```

**Best Practices:**

- Cache API results to reduce costs
- Use timeouts and fallbacks for reliability
- Implement background enrichment workers for database enhancement
- Configure rate limits and cost controls

## Database Enrichment

For applications requiring detailed email intelligence, implement background enrichment:

### Architecture Pattern

```
User Action → Real-time Validation → Queue for Enrichment
                                          ↓
Background Worker → Third-party APIs → Database Update
```

### Benefits

- **Cost Efficiency**: Batch and cache API calls
- **Performance**: Non-blocking user experience
- **Intelligence**: Rich email metadata for analytics and fraud prevention
- **Flexibility**: Enable/disable enrichment per use case

### Implementation Example

```javascript
// Queue email for background enrichment
await enrichmentQueue.add("validate-email", {
  email: user.email,
  userId: user.id,
  priority: user.isVip ? "high" : "normal",
});

// Background worker processes queue
worker.process("validate-email", async (job) => {
  const result = await thirdPartyValidation(job.data.email);
  await database.saveEnrichment(job.data.userId, result);
});
```

## Performance Characteristics

- **Cold Start**: < 100ms (Cloudflare Workers)
- **Validation Time**: < 50ms (local checks)
- **MX Resolution**: < 200ms (DNS lookup)
- **Third-party APIs**: 1-5s (when enabled, cached results < 10ms)
- **Concurrent Requests**: Scales automatically
- **Background Enrichment**: Non-blocking, queue-based processing

## Security & Privacy

- Input validation with Zod schemas
- Email masking in logs for privacy protection
- API keys secured via environment variables
- Rate limiting to prevent abuse
- No sensitive data exposed in public API responses
- Optional third-party integration (user-controlled)

## Cost Optimization

### Third-Party API Usage

- **Local-First**: Perform comprehensive local validation before API calls
- **Caching**: Store results to avoid duplicate API requests
- **Selective Enrichment**: Only enrich emails that require additional validation
- **Batch Processing**: Use background workers for non-urgent enrichment
- **Fallback Logic**: Graceful degradation when APIs are unavailable

### Recommended Approach

1. Use local validation for 90%+ of cases
2. Cache third-party results for 24-48 hours
3. Implement daily/monthly API usage caps
4. Monitor and alert on unusual API usage patterns
