{"name": "api", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings"}, "dependencies": {"@scalar/hono-api-reference": "^0.9.7", "email-validator": "^2.0.4", "extract-domain": "^5.0.2", "hono": "^4.8.2", "node-fetch": "^3.3.2", "p-queue": "^8.1.0", "whoiser": "2.0.0-beta.6", "zod": "^3.25.67"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250620.0", "@types/node": "^24.0.3", "wrangler": "^4.22.0"}}