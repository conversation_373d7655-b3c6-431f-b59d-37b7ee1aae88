{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "pnpx tsx src/scripts/seed.ts"}, "dependencies": {"@better-fetch/fetch": "^1.1.18", "@hookform/resolvers": "^5.0.1", "@prisma/client": "6.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.80.7", "@types/react-syntax-highlighter": "^15.5.13", "argon2": "^0.43.0", "better-auth": "^1.2.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "database": "workspace:*", "date-fns": "^4.1.0", "ioredis": "^5.6.1", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "rate-limiter-flexible": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "^6.7.0", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}