import { getRequestMetadata } from '@/lib/api/request';
import { trackValidationApiUsage } from '@/lib/db/api-usage';
import { deductUserCredit, refundUserCredit } from '@/lib/db/credits';
import {
  getProjectRulesForApi,
  getProjectValidationRules,
} from '@/lib/db/rules';
import {
  consumeConcurrencyLimit,
  consumeRateLimit,
  releaseConcurrency,
} from '@/lib/limiter';
import { SignalAction } from 'database';
import { NextRequest, NextResponse } from 'next/server';
import { RequestBodySchema } from './schema';

type ErrorResponseMetadata = {
  projectId?: string;
  userId?: string;
  apiKeyId?: string;
  ipAddress?: string;
  userAgent?: string;
};

type ValidationApiResponse = {
  isValid: boolean;
  email: string;
  reason: string;
  checks: {
    isWhitelisted: boolean | null;
    isBlacklisted: boolean | null;
    isDisposable: boolean | null;
    hasMxRecords: boolean | null;
    syntaxValid: boolean | null;
    isRoleBased: boolean | null;
    riskScore: number;
  };
  reasons: string[];
};

/**
 * Transforms the validation API response to the expected frontend format
 */
function transformValidationResponse(apiResponse: ValidationApiResponse) {
  const { isValid, checks, reasons } = apiResponse;
  const { riskScore } = checks;

  // Determine status based on validity and risk score
  let status: 'valid' | 'invalid' | 'risky' = 'valid';
  if (!isValid) {
    status = 'invalid';
  } else if (riskScore > 50) {
    status = 'risky';
  }

  // Check for temporary email domain
  const temporaryEmail =
    reasons.includes('temporary_email_domain') ||
    checks.isDisposable === true ||
    reasons.some(
      (reason) =>
        reason.toLowerCase().includes('disposable') ||
        reason.toLowerCase().includes('temporary')
    );

  // Determine action based on status
  const action = status === 'valid' ? SignalAction.ALLOW : SignalAction.DENY;

  return {
    emailRiskScore: riskScore,
    status,
    reasons,
    temporaryEmail,
    action: action.toLowerCase(),
  };
}

export async function POST(request: NextRequest) {
  const metadata = await getRequestMetadata(request);

  // Use userId if available, otherwise IP address
  const key = metadata?.userId || metadata?.ipAddress || 'anonymous';

  // Check rate limit
  try {
    await consumeRateLimit(key);
  } catch (rejRes) {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }

  // Check concurrency limit
  let concurrencyRes;
  try {
    concurrencyRes = await consumeConcurrencyLimit(key);
  } catch (rejRes) {
    return NextResponse.json(
      { error: 'Too many concurrent requests. Please try again later.' },
      { status: 429 }
    );
  }

  if (!metadata?.authenticated) {
    return NextResponse.json(
      {
        statusCode: 401,
        timestamp: new Date().toISOString(),
        message: 'Invalid API key',
      },
      { status: 401 }
    );
  }

  // Deduct credit before proceeding
  try {
    await deductUserCredit(metadata.userId!);
  } catch (err: any) {
    return NextResponse.json(
      { error: 'No credits left. Please upgrade your plan or add credits.' },
      { status: 402 }
    );
  }

  const requestBody = await request.json().catch(() => ({}));

  const validation = RequestBodySchema.safeParse(requestBody);
  if (!validation.success) {
    // Refund credit for validation failure
    await refundUserCredit(metadata.userId!);
    return NextResponse.json(
      {
        statusCode: 400,
        timestamp: new Date().toISOString(),
        message: 'Validation failed',
        paths: validation.error.issues.map((issue) => ({
          path: issue.path[0],
          message: issue.message,
        })),
      },
      { status: 400 }
    );
  }

  const { email } = validation.data;

  try {
    // Fetch project rules and validation settings
    const [projectRules, validationRules] = await Promise.all([
      getProjectRulesForApi(metadata.projectId!),
      getProjectValidationRules(metadata.projectId!),
    ]);

    // Make request to the new Hono email validator API
    const validatorResponse = await fetch(
      `${process.env.EMAIL_VALIDATOR_URL || 'http://localhost:8787'}/validate`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          userRules: validationRules,
          userWhitelist: projectRules.userWhitelist,
          userBlacklist: projectRules.userBlacklist,
        }),
      }
    );

    if (!validatorResponse.ok) {
      // Refund credit for upstream service error
      console.error('Error validating email:', validatorResponse);
      await refundUserCredit(metadata.userId!);

      // Try to parse error response from API
      let errorMessage = 'Error validating email';
      try {
        const errorData = await validatorResponse.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch {
        // Use default error message if response is not JSON
      }

      return await sendErrorResponse(
        metadata as ErrorResponseMetadata,
        errorMessage,
        { email }
      );
    }

    const validationResult: ValidationApiResponse =
      await validatorResponse.json();
    console.log(
      'Validation result:',
      JSON.stringify(validationResult, null, 2)
    );

    // Transform the API response to match frontend expectations
    const transformedResult = transformValidationResponse(validationResult);

    console.log(
      `Email: ${email}, Status: ${transformedResult.status}, Risk Score: ${transformedResult.emailRiskScore}, Reasons: ${transformedResult.reasons.join(', ')}`
    );

    try {
      await trackValidationApiUsage({
        action:
          transformedResult.status === 'valid'
            ? SignalAction.ALLOW
            : SignalAction.DENY,
        projectId: metadata.projectId!,
        email,
        riskScore: transformedResult.emailRiskScore,
        reasons: transformedResult.reasons,
        metadata: {
          ipAddress: metadata.ipAddress,
          userAgent: metadata.userAgent,
        },
        apiKeyId: metadata.apiKeyId,
        userId: metadata.userId!,
        ipAddress: metadata.ipAddress,
      });
    } catch (trackingError) {
      // Log the error but don't fail the request
      console.error('Error tracking validation usage:', trackingError);
    }

    // Release concurrency slot
    await releaseConcurrency(key);

    return NextResponse.json(transformedResult);
  } catch (error) {
    console.error('Error in email validation API:', error);
    // Refund credit for internal server error
    await refundUserCredit(metadata.userId!);
    await releaseConcurrency(key); // Decrement concurrency count on error
    return await sendErrorResponse(
      metadata as ErrorResponseMetadata,
      'Internal server error',
      { email }
    );
  }
}

/**
 * Helper function to handle error responses with proper tracking
 * @param metadata Request metadata from authentication
 * @param errorMessage Error message to return to the client
 * @param params Additional parameters related to the request
 * @returns NextResponse with error details
 */
async function sendErrorResponse(
  metadata: ErrorResponseMetadata,
  errorMessage: string,
  params: Record<string, string>
) {
  try {
    await trackValidationApiUsage({
      action: SignalAction.DENY,
      projectId: metadata.projectId!,
      email: params.email,
      riskScore: 100, // High risk score for errors
      reasons: [errorMessage],
      metadata: {
        ipAddress: metadata.ipAddress,
        userAgent: metadata.userAgent,
      },
      apiKeyId: metadata.apiKeyId,
      userId: metadata.userId,
      ipAddress: metadata.ipAddress,
      errorMessage,
    });
  } catch (trackingError) {
    console.error('Error tracking validation error:', trackingError);
  }

  return NextResponse.json({ error: errorMessage }, { status: 400 });
}
