'use client';
// TODO: Refactor this page to use the new UI components

import {
  addRuleEntryAction,
  getRuleEntriesAction,
  removeRuleEntryAction,
} from '@/app/dashboard/lists/actions';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { RuleEntry } from 'database';
import { Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export default function WhitelistBlacklistPage() {
  const queryClient = useQueryClient();
  const [isWhitelistModalOpen, setIsWhitelistModalOpen] = useState(false);
  const [isBlacklistModalOpen, setIsBlacklistModalOpen] = useState(false);
  const [newEntryValue, setNewEntryValue] = useState('');
  const [newEntryType, setNewEntryType] = useState<'email' | 'domain'>('email');
  const [addingListType, setAddingListType] = useState<
    'WHITELIST' | 'BLACKLIST'
  >('WHITELIST');

  const {
    data: whitelistEntries,
    isLoading: whitelistLoading,
    refetch: refetchWhitelist,
  } = useQuery<RuleEntry[]>({
    queryKey: ['rules', 'WHITELIST'],
    queryFn: () => getRuleEntriesAction('WHITELIST'),
  });
  const {
    data: blacklistEntries,
    isLoading: blacklistLoading,
    refetch: refetchBlacklist,
  } = useQuery<RuleEntry[]>({
    queryKey: ['rules', 'BLACKLIST'],
    queryFn: () => getRuleEntriesAction('BLACKLIST'),
  });

  const addMutation = useMutation({
    mutationFn: async () => {
      return addRuleEntryAction({
        type: newEntryType === 'email' ? 'EMAIL' : 'DOMAIN',
        value: newEntryValue,
        listType: addingListType,
      });
    },
    onSuccess: (result) => {
      if (result?.error) {
        toast.error(result.error);
      } else {
        toast.success(
          `${newEntryType} added to ${addingListType.toLowerCase()}`
        );
        setNewEntryValue('');
        setNewEntryType('email');
        setIsWhitelistModalOpen(false);
        setIsBlacklistModalOpen(false);
        queryClient.invalidateQueries({ queryKey: ['rules', addingListType] });
      }
    },
    onError: () => toast.error('Failed to add rule'),
  });

  const removeMutation = useMutation({
    mutationFn: async (id: string) => removeRuleEntryAction({ id }),
    onSuccess: (result, id, variables) => {
      if (result?.error) {
        toast.error(result.error);
      } else {
        toast.success('Entry removed');
        queryClient.invalidateQueries({ queryKey: ['rules', 'WHITELIST'] });
        queryClient.invalidateQueries({ queryKey: ['rules', 'BLACKLIST'] });
      }
    },
    onError: () => toast.error('Failed to remove rule'),
  });

  const handleAddEntry = (listType: 'WHITELIST' | 'BLACKLIST') => {
    setAddingListType(listType);
    addMutation.mutate();
  };

  const handleRemoveEntry = (id: string) => {
    removeMutation.mutate(id);
  };

  return (
    <>
      <header className="sticky top-0 z-10 border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center space-x-4">
          <SidebarTrigger />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Whitelist & Blacklist
            </h1>
            <p className="text-sm text-gray-600">
              Manage trusted and blocked email addresses and domains
            </p>
          </div>
        </div>
      </header>

      <main className="p-6">
        <Tabs defaultValue="whitelist" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:w-auto lg:grid-cols-2">
            <TabsTrigger value="whitelist">Whitelist</TabsTrigger>
            <TabsTrigger value="blacklist">Blacklist</TabsTrigger>
          </TabsList>

          <TabsContent value="whitelist" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    Whitelist Entries
                  </CardTitle>
                  <Button onClick={() => setIsWhitelistModalOpen(true)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add to Whitelist
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                  <Input placeholder="Search whitelist..." className="pl-10" />
                </div>

                <div className="space-y-2">
                  {whitelistEntries?.map((entry) => (
                    <div
                      key={entry.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <Badge
                          variant="outline"
                          className="border-green-300 text-green-700"
                        >
                          {entry.type === 'EMAIL' ? 'Email' : 'Domain'}
                        </Badge>
                        <span className="font-mono text-sm">{entry.value}</span>
                      </div>
                      <div className="flex items-center space-x-3 text-sm text-gray-600">
                        <span>
                          {new Date(entry.createdAt).toLocaleDateString()}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleRemoveEntry(entry.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="blacklist" className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-gray-900">
                    Blacklist Entries
                  </CardTitle>
                  <Button
                    variant="destructive"
                    onClick={() => setIsBlacklistModalOpen(true)}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add to Blacklist
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="relative">
                  <Search className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                  <Input placeholder="Search blacklist..." className="pl-10" />
                </div>

                <div className="space-y-2">
                  {blacklistEntries?.map((entry) => (
                    <div
                      key={entry.id}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center space-x-3">
                        <Badge variant="destructive">
                          {entry.type === 'EMAIL' ? 'Email' : 'Domain'}
                        </Badge>
                        <span className="font-mono text-sm">{entry.value}</span>
                      </div>
                      <div className="flex items-center space-x-3 text-sm text-gray-600">
                        <span>
                          {new Date(entry.createdAt).toLocaleDateString()}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleRemoveEntry(entry.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      <Dialog
        open={isWhitelistModalOpen}
        onOpenChange={setIsWhitelistModalOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add to Whitelist</DialogTitle>
            <DialogDescription>
              Add an email address or domain to the whitelist to ensure emails
              are always trusted.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="whitelist-type">Type</Label>
              <RadioGroup
                value={newEntryType}
                onValueChange={(val) =>
                  setNewEntryType(val as 'email' | 'domain')
                }
                className="flex space-x-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="email" id="whitelist-email" />
                  <Label htmlFor="whitelist-email">Email</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="domain" id="whitelist-domain" />
                  <Label htmlFor="whitelist-domain">Domain</Label>
                </div>
              </RadioGroup>
            </div>
            <div className="space-y-2">
              <Label htmlFor="whitelist-value">
                {newEntryType === 'email' ? 'Email Address' : 'Domain'}
              </Label>
              <Input
                id="whitelist-value"
                placeholder={
                  newEntryType === 'email'
                    ? '<EMAIL>'
                    : '*.example.com'
                }
                value={newEntryValue}
                onChange={(e) => setNewEntryValue(e.target.value)}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsWhitelistModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleAddEntry('WHITELIST')}
                className="bg-green-600 hover:bg-green-700"
              >
                Add to Whitelist
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isBlacklistModalOpen}
        onOpenChange={setIsBlacklistModalOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add to Blacklist</DialogTitle>
            <DialogDescription>
              Add an email address or domain to the blacklist to block emails
              from these sources.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="blacklist-type">Type</Label>
              <RadioGroup
                value={newEntryType}
                onValueChange={(val) =>
                  setNewEntryType(val as 'email' | 'domain')
                }
                className="flex space-x-6"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="email" id="blacklist-email" />
                  <Label htmlFor="blacklist-email">Email</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="domain" id="blacklist-domain" />
                  <Label htmlFor="blacklist-domain">Domain</Label>
                </div>
              </RadioGroup>
            </div>
            <div className="space-y-2">
              <Label htmlFor="blacklist-value">
                {newEntryType === 'email' ? 'Email Address' : 'Domain'}
              </Label>
              <Input
                id="blacklist-value"
                placeholder={
                  newEntryType === 'email'
                    ? '<EMAIL>'
                    : '*.spam-domain.com'
                }
                value={newEntryValue}
                onChange={(e) => setNewEntryValue(e.target.value)}
              />
            </div>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setIsBlacklistModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleAddEntry('BLACKLIST')}
                variant="destructive"
              >
                Add to Blacklist
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
