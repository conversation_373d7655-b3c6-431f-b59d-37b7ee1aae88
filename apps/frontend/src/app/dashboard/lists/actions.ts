'use server';

import { getProjectForUser, getUser } from '@/lib/db/queries';
import { addRuleEntry, deleteRuleEntry, getProjectRules } from '@/lib/db/rules';
import { RuleListType, RuleType } from 'database';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

const addRuleSchema = z.object({
  type: z.enum(['EMAIL', 'DOMAIN']),
  value: z.string().min(1),
  listType: z.enum(['BLACKLIST', 'WHITELIST']),
});

const removeRuleSchema = z.object({
  id: z.string(),
});

export async function addRuleEntryAction(data: {
  type: RuleType;
  value: string;
  listType: RuleListType;
}) {
  try {
    const validatedFields = addRuleSchema.safeParse(data);

    if (!validatedFields.success) {
      return {
        error: 'Invalid input. Please check the fields.',
        details: validatedFields.error.flatten().fieldErrors,
      };
    }

    const user = await getUser();

    if (!user) {
      return {
        error: 'You must be logged in to manage rules.',
      };
    }

    const project = await getProjectForUser(user.id);

    if (!project) {
      return {
        error: 'You need to have a project to manage rules.',
      };
    }

    await addRuleEntry({
      projectId: project.id,
      type: validatedFields.data.type as 'EMAIL' | 'DOMAIN',
      value: validatedFields.data.value,
      listType: validatedFields.data.listType as 'WHITELIST' | 'BLACKLIST',
      userId: user.id,
    });

    revalidatePath('/dashboard/lists');
    return { success: true };
  } catch (error) {
    return {
      error: 'Failed to add rule entry.',
    };
  }
}

export async function removeRuleEntryAction(data: { id: string }) {
  try {
    const validatedFields = removeRuleSchema.safeParse(data);

    if (!validatedFields.success) {
      return {
        error: 'Invalid input.',
      };
    }

    const user = await getUser();

    if (!user) {
      return {
        error: 'You must be logged in to manage rules.',
      };
    }

    const project = await getProjectForUser(user.id);

    if (!project) {
      return {
        error: 'You need to have a project to manage rules.',
      };
    }

    await deleteRuleEntry(validatedFields.data.id, project.id, user.id);

    revalidatePath('/dashboard/lists');
    return { success: true };
  } catch (error) {
    return {
      error: 'Failed to remove rule entry.',
    };
  }
}

export async function getRuleEntriesAction(listType?: RuleListType) {
  const user = await getUser();
  if (!user) return [];

  const project = await getProjectForUser(user.id);
  if (!project) return [];

  const allRules = await getProjectRules(project.id);

  // Filter by list type if specified
  if (listType) {
    return allRules.filter((rule) => rule.listType === listType);
  }

  return allRules;
}
