'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Calendar, CreditCard, Crown, TrendingUp } from 'lucide-react';
import {
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

export default function CreditsPage() {
  const usageData = [
    { date: '06/09', credits: 1200 },
    { date: '06/10', credits: 1800 },
    { date: '06/11', credits: 1500 },
    { date: '06/12', credits: 2200 },
    { date: '06/13', credits: 1900 },
  ];

  const transactions = [
    {
      id: 1,
      type: 'usage',
      amount: -150,
      description: 'Email validations',
      date: '2024-06-13 14:30',
    },
    {
      id: 2,
      type: 'purchase',
      amount: +10000,
      description: 'Credit top-up',
      date: '2024-06-13 09:15',
    },
    {
      id: 3,
      type: 'usage',
      amount: -89,
      description: 'Bulk validation',
      date: '2024-06-12 16:22',
    },
    {
      id: 4,
      type: 'usage',
      amount: -45,
      description: 'API calls',
      date: '2024-06-12 11:10',
    },
  ];

  return (
    <>
      <header className="sticky top-0 z-10 border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <SidebarTrigger />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Credits Management
              </h1>
              <p className="text-sm text-gray-600">
                Monitor and manage your API credits
              </p>
            </div>
          </div>
          <Button className="bg-purple-600 hover:bg-purple-700">
            <CreditCard className="mr-2 h-4 w-4" />
            Buy Credits
          </Button>
        </div>
      </header>

      <main className="space-y-6 p-6">
        {/* Current Status */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Crown className="h-5 w-5 text-purple-600" />
                <span>Current Plan: Premium</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Credits Used</span>
                  <span className="font-medium">18,423 / 100,000</span>
                </div>
                <Progress value={18.4} className="h-3" />
                <div className="flex justify-between text-xs text-gray-600">
                  <span>81,577 credits remaining</span>
                  <span>Resets in 17 days</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4">
                <div className="rounded-lg bg-green-50 p-3 text-center">
                  <div className="text-2xl font-bold text-green-600">
                    $0.0008
                  </div>
                  <div className="text-sm text-gray-600">
                    Cost per validation
                  </div>
                </div>
                <div className="rounded-lg bg-blue-50 p-3 text-center">
                  <div className="text-2xl font-bold text-blue-600">$14.74</div>
                  <div className="text-sm text-gray-600">Spent this month</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full bg-purple-600 hover:bg-purple-700">
                <CreditCard className="mr-2 h-4 w-4" />
                Buy 10,000 Credits
              </Button>
              <Button variant="outline" className="w-full">
                <Crown className="mr-2 h-4 w-4" />
                Upgrade Plan
              </Button>
              <Button variant="outline" className="w-full">
                <Calendar className="mr-2 h-4 w-4" />
                View Billing History
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Usage Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Credits Usage Trend</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={usageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="credits"
                  stroke="#7C3AED"
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Transaction History */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {transactions.map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className={`h-2 w-2 rounded-full ${
                        transaction.type === 'purchase'
                          ? 'bg-green-500'
                          : 'bg-red-500'
                      }`}
                    />
                    <div>
                      <div className="font-medium text-gray-900">
                        {transaction.description}
                      </div>
                      <div className="text-sm text-gray-600">
                        {transaction.date}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`font-medium ${
                        transaction.amount > 0
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}
                    >
                      {transaction.amount > 0 ? '+' : ''}
                      {transaction.amount.toLocaleString()} credits
                    </span>
                    <Badge
                      variant={
                        transaction.type === 'purchase'
                          ? 'default'
                          : 'secondary'
                      }
                    >
                      {transaction.type}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </main>
    </>
  );
}
