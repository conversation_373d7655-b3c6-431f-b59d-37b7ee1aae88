import AppSidebar from '@/components/app-sidebar';
import { ReactQueryProvider } from '@/components/ReactQueryProvider';
import { RefetchProvider } from '@/components/refetch-context';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ReactQueryProvider>
      <SidebarProvider>
        <RefetchProvider>
          <div className="flex min-h-screen w-full bg-gray-50">
            <AppSidebar />
            <SidebarInset className="flex-1">{children}</SidebarInset>
          </div>
        </RefetchProvider>
      </SidebarProvider>
    </ReactQueryProvider>
  );
}
