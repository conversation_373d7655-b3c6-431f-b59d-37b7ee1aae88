'use server';

import { ActivityType, Role } from 'database';
import { auth } from '@/lib/auth';
import { createActivity } from '@/lib/db/activities';
import { getUser } from '@/lib/db/queries';
import prisma from '@/lib/prisma';
import { verifyPassword } from '@/utils/password';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import { z } from 'zod';

const updatePasswordSchema = z
  .object({
    currentPassword: z.string().min(8).max(100),
    newPassword: z.string().min(8).max(100),
    confirmPassword: z.string().min(8).max(100),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

export async function updatePassword(data: {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}) {
  try {
    const validatedFields = updatePasswordSchema.safeParse(data);
    if (!validatedFields.success) {
      return {
        error: validatedFields.error.errors[0].message,
      };
    }

    const { currentPassword, newPassword } = validatedFields.data;

    if (currentPassword === newPassword) {
      return {
        error: 'New password must be different from the current password.',
      };
    }

    const user = await getUser();
    if (!user) {
      return { error: 'You must be logged in.' };
    }

    try {
      await auth.api.changePassword({
        headers: await headers(),
        body: {
          currentPassword,
          newPassword,
        },
      });
    } catch (error) {
      if (error instanceof Error) {
        return { error: error.message || 'Current password is incorrect.' };
      }
      return { error: 'Failed to update password.' };
    }
    await createActivity({
      userId: user.id,
      action: ActivityType.UPDATE_PASSWORD,
    });

    return { success: 'Password updated successfully.' };
  } catch (error) {
    console.error('Password update error:', error);
    return { error: 'An unexpected error occurred.' };
  }
}

const deleteAccountSchema = z.object({
  password: z.string().min(8).max(100),
});

export async function deleteAccount(data: { password: string }) {
  try {
    const validatedFields = deleteAccountSchema.safeParse(data);
    if (!validatedFields.success) {
      return {
        error: validatedFields.error.errors[0].message,
      };
    }

    const { password } = validatedFields.data;

    const user = await getUser();
    if (!user) {
      return { error: 'You must be logged in.' };
    }

    try {
      const account = await prisma.account.findFirst({
        where: {
          userId: user.id,
          providerId: 'credential',
        },
      });

      if (!account) {
        return { error: 'Account not found.' };
      }
      const isPasswordValid = await verifyPassword(password, account.password!);

      if (!isPasswordValid) {
        return { error: 'Incorrect password.' };
      }
    } catch (error) {
      return { error: 'Incorrect password. Account deletion failed.' };
    }

    await createActivity({
      userId: user.id,
      action: ActivityType.DELETE_ACCOUNT,
    });

    await auth.api.deleteUser({
      body: {
        password,
      },
    });

    redirect('/sign-in');
  } catch (error) {
    if (isRedirectError(error)) {
      throw error;
    }
    console.error('Account deletion error:', error);
    return { error: 'An unexpected error occurred.' };
  }
}

const updateAccountSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  email: z.string().email('Invalid email address'),
});

export async function updateAccount(data: { name: string; email: string }) {
  try {
    const validatedFields = updateAccountSchema.safeParse(data);
    if (!validatedFields.success) {
      return {
        error: validatedFields.error.errors[0].message,
      };
    }

    const { name, email } = validatedFields.data;

    const user = await getUser();
    if (!user) {
      return { error: 'You must be logged in.' };
    }

    await Promise.all([
      await auth.api.updateUser({
        headers: await headers(),
        body: {
          name,
        },
      }),
      createActivity({
        userId: user.id,
        action: ActivityType.UPDATE_ACCOUNT,
      }),
    ]);

    return { success: 'Account updated successfully.' };
  } catch (error) {
    console.error('Account update error:', error);
    return { error: 'An unexpected error occurred.' };
  }
}
