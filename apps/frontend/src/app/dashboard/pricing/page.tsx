'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Switch } from '@/components/ui/switch';
import { Check, Crown, Shield, Star, Zap } from 'lucide-react';
import { useState } from 'react';

export default function PricingPage() {
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Starter',
      icon: Zap,
      price: { monthly: 19, annual: 15 },
      credits: '10,000',
      features: [
        '10,000 email validations per month',
        'Basic email verification',
        'API access',
        'Email support',
        'Basic analytics',
      ],
      popular: false,
    },
    {
      name: 'Professional',
      icon: Crown,
      price: { monthly: 49, annual: 39 },
      credits: '50,000',
      features: [
        '50,000 email validations per month',
        'Advanced verification rules',
        'Whitelist/Blacklist management',
        'Priority API access',
        'Advanced analytics',
        'Priority support',
        'Custom integrations',
      ],
      popular: true,
    },
    {
      name: 'Enterprise',
      icon: Shield,
      price: { monthly: 149, annual: 119 },
      credits: '200,000',
      features: [
        '200,000 email validations per month',
        'Custom verification rules',
        'Advanced security features',
        'Dedicated IP addresses',
        'Custom reporting',
        '24/7 phone support',
        'SLA guarantee',
        'Custom onboarding',
      ],
      popular: false,
    },
  ];

  return (
    <>
      <header className="sticky top-0 z-10 border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center space-x-4">
          <SidebarTrigger />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Pricing & Subscription
            </h1>
            <p className="text-sm text-gray-600">
              Choose the perfect plan for your email validation needs
            </p>
          </div>
        </div>
      </header>

      <main className="space-y-8 p-6">
        {/* Current Plan */}
        <Card className="border-purple-200 bg-purple-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Crown className="h-5 w-5 text-purple-600" />
              <span>Current Plan: Professional</span>
              <Badge className="bg-purple-600">Active</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <div className="text-2xl font-bold text-purple-600">50,000</div>
                <div className="text-sm text-gray-600">Credits per month</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">$49</div>
                <div className="text-sm text-gray-600">Per month</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">
                  17 days
                </div>
                <div className="text-sm text-gray-600">Until next billing</div>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline">Manage Subscription</Button>
              <Button variant="outline">View Invoice History</Button>
            </div>
          </CardContent>
        </Card>

        {/* Billing Toggle */}
        <div className="flex items-center justify-center space-x-4">
          <span
            className={`text-sm ${!isAnnual ? 'font-medium' : 'text-gray-600'}`}
          >
            Monthly
          </span>
          <Switch checked={isAnnual} onCheckedChange={setIsAnnual} />
          <span
            className={`text-sm ${isAnnual ? 'font-medium' : 'text-gray-600'}`}
          >
            Annual{' '}
            <Badge variant="secondary" className="ml-1">
              Save 20%
            </Badge>
          </span>
        </div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative ${plan.popular ? 'border-purple-300 shadow-lg' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 transform">
                  <Badge className="bg-purple-600 px-4 py-1 text-white">
                    <Star className="mr-1 h-3 w-3" />
                    Most Popular
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center">
                <div className="mb-2 flex items-center justify-center space-x-2">
                  <plan.icon className="h-6 w-6 text-purple-600" />
                  <CardTitle className="text-xl">{plan.name}</CardTitle>
                </div>
                <div className="space-y-1">
                  <div className="text-4xl font-bold">
                    ${isAnnual ? plan.price.annual : plan.price.monthly}
                    <span className="text-lg font-normal text-gray-600">
                      /month
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {plan.credits} validations included
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-start space-x-2"
                    >
                      <Check className="mt-0.5 h-4 w-4 flex-shrink-0 text-green-500" />
                      <span className="text-sm text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full ${plan.popular ? 'bg-purple-600 hover:bg-purple-700' : ''}`}
                  variant={plan.popular ? 'default' : 'outline'}
                >
                  {plan.name === 'Professional'
                    ? 'Current Plan'
                    : `Upgrade to ${plan.name}`}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Enterprise Contact */}
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50">
          <CardContent className="p-8 text-center">
            <h3 className="mb-2 text-2xl font-bold text-gray-900">
              Need more than 200,000 validations?
            </h3>
            <p className="mb-4 text-gray-600">
              Contact our sales team for custom enterprise pricing and dedicated
              support.
            </p>
            <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
              Contact Sales
            </Button>
          </CardContent>
        </Card>
      </main>
    </>
  );
}
