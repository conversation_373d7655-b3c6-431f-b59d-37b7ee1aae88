'use server';

import {
  create<PERSON><PERSON><PERSON><PERSON>,
  getProject<PERSON><PERSON><PERSON><PERSON>,
  getUser,
  revokeApi<PERSON><PERSON>,
} from '@/lib/db';
import { revalidatePath } from 'next/cache';
import { headers } from 'next/headers';
import { z } from 'zod';

const createApiKeySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
});

const revokeApiKeySchema = z.object({
  id: z.string(),
});

export async function createApiKeyAction(data: { name: string }) {
  try {
    const validatedFields = createApiKeySchema.safeParse(data);

    if (!validatedFields.success) {
      return {
        error: 'Invalid input. Please check the fields.',
        details: validatedFields.error.flatten().fieldErrors,
      };
    }

    const user = await getUser();

    if (!user) {
      return {
        error: 'You must be logged in to create API keys.',
      };
    }

    const project = await getProjectForUser(user.id);

    if (!project) {
      return {
        error: 'You need to have a project to create API keys.',
      };
    }

    const headersList = await headers();
    const ipAddress = headersList.get('x-forwarded-for') || 'unknown';

    const apiKey = await createApiKey(
      project.id,
      user.id,
      validatedFields.data.name,
      ipAddress
    );

    revalidatePath('/dashboard/api-keys');

    return {
      success: true,
      message: 'API key created successfully.',
      apiKey: apiKey.key, // Return the key so it can be displayed once
    };
  } catch (error) {
    console.error('Error creating API key:', error);
    return {
      error: 'Failed to create API key. Please try again.',
    };
  }
}

export async function revokeApiKeyAction(data: { id: string }) {
  try {
    const validatedFields = revokeApiKeySchema.safeParse(data);

    if (!validatedFields.success) {
      return {
        error: 'Invalid input.',
        details: validatedFields.error.flatten().fieldErrors,
      };
    }

    const user = await getUser();

    if (!user) {
      return {
        error: 'You must be logged in to revoke API keys.',
      };
    }

    const project = await getProjectForUser(user.id);

    if (!project) {
      return {
        error: 'You need to have a project to revoke API keys.',
      };
    }

    const headersList = await headers();
    const ipAddress = headersList.get('x-forwarded-for') || 'unknown';

    const revokedKey = await revokeApiKey(
      validatedFields.data.id,
      project.id,
      user.id,
      ipAddress
    );

    if (!revokedKey) {
      return {
        error: 'API key not found or already revoked.',
      };
    }

    revalidatePath('/dashboard/api-keys');

    return {
      success: true,
      message: 'API key revoked successfully.',
    };
  } catch (error) {
    console.error('Error revoking API key:', error);
    return {
      error: 'Failed to revoke API key. Please try again.',
    };
  }
}
