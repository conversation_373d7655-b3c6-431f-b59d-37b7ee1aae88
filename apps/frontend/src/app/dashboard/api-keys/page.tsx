import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from '@/components/ui/card';
import { getProjectApiKeys } from '@/lib/db/api-keys';
import { getProjectForUser, getUser } from '@/lib/db/queries';
import { AlertTriangle } from 'lucide-react';
import { Suspense } from 'react';
import { ApiKeysList } from './(components)/api-keys-list';
import { CodeBlock } from './(components)/code-block';
import { CreateApiKeyForm } from './(components)/create-api-key-form';
import { ApiKeysHeader } from './header';

export default async function ApiKeysPage() {
  const user = await getUser();

  if (!user) {
    return (
      <div className="flex-1 p-4 lg:p-8">
        <h1 className="text-2xl font-bold text-gray-900">API Keys</h1>
        <p className="mt-2 text-gray-500">
          You need to be logged in to manage API keys.
        </p>
      </div>
    );
  }

  const project = await getProjectForUser(user.id);

  if (!project) {
    return (
      <div className="flex-1 p-4 lg:p-8">
        <h1 className="text-2xl font-bold text-gray-900">API Keys</h1>
        <p className="mt-2 text-gray-500">
          You are not part of any project. Please create or join a project to
          manage API keys.
        </p>
      </div>
    );
  }

  const apiKeys = await getProjectApiKeys(project.id);

  return (
    <>
      <ApiKeysHeader />
      <main className="flex-1 space-y-6 p-4 lg:p-8">
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <CardTitle className="text-lg font-semibold text-orange-900">
                Security Best Practices
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-orange-800">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <p className="font-medium">🔐 Keep your API keys secure:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Never share your API keys in public repositories</li>
                  <li>• Store keys in environment variables</li>
                  <li>• Regenerate keys if compromised</li>
                </ul>
              </div>
              <div className="space-y-2">
                <p className="font-medium">⚡ Monitor usage:</p>
                <ul className="space-y-1 text-xs">
                  <li>• Regularly review API key usage</li>
                  <li>• Revoke unused or old keys</li>
                  <li>• Set up alerts for unusual activity</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Create API Key</CardTitle>
              <CardDescription>
                Create a new API key for your team. You will only be able to
                view the key once after creation.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CreateApiKeyForm />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Your API Keys</CardTitle>
              <CardDescription>
                Manage your existing API keys. Revoked keys cannot be used to
                authenticate.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>Loading API keys...</div>}>
                <ApiKeysList apiKeys={apiKeys} />
              </Suspense>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>API Usage Example</CardTitle>
            <CardDescription>
              Learn how to validate emails using our API. Copy the examples
              below and replace &lt;your-api-key&gt; with your actual API key.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <CodeBlock
                showLineNumbers
                language="bash"
                code={`curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer <your-api-key>" \\
  -d '{"email": "<EMAIL>"}' \\
  ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/verification`}
              />

              <CodeBlock
                showLineNumbers
                language="json"
                code={`{
  "emailRiskScore": 15,
  "status": "valid",
  "reasons": ["validated"],
  "temporaryEmail": false,
  "action": "allow"
}`}
              />

              <div>
                <h4 className="mb-2 text-sm font-medium text-gray-900">
                  Response Fields
                </h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-3">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                      emailRiskScore
                    </code>
                    <span className="text-gray-600">
                      Risk score from 0 (safe) to 100 (risky)
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                      status
                    </code>
                    <span className="text-gray-600">
                      Overall email status: valid, invalid, or risky
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                      reasons
                    </code>
                    <span className="text-gray-600">
                      Array of specific validation results or issues found
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                      temporaryEmail
                    </code>
                    <span className="text-gray-600">
                      Whether email uses a temporary/disposable domain
                    </span>
                  </div>
                  <div className="flex items-center gap-3">
                    <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                      action
                    </code>
                    <span className="text-gray-600">
                      Recommended action: allow or deny
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </>
  );
}
