'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { showToast } from '@/components/ui/sonner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ApiKey } from 'database';
import { formatDistanceToNow } from 'date-fns';
import { Key, Trash2 } from 'lucide-react';
import { useState } from 'react';
import { revokeApiKeyAction } from '../actions';

type ApiKeysListProps = {
  apiKeys: ApiKey[];
};

export function ApiKeysList({ apiKeys }: ApiKeysListProps) {
  const [isRevoking, setIsRevoking] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedKeyId, setSelectedKeyId] = useState<string | null>(null);

  const handleRevoke = async (id: string) => {
    setIsRevoking(true);

    const result = await revokeApiKeyAction({ id });

    if (result.error) {
      showToast({
        variant: 'error',
        text: result.error,
      });
    } else {
      showToast({
        text: result.message!,
        variant: 'success',
      });
    }

    setIsRevoking(false);
    setOpenDialog(false);
  };

  const confirmRevoke = (id: string) => {
    setSelectedKeyId(id);
    setOpenDialog(true);
  };

  if (apiKeys.length === 0) {
    return (
      <div className="py-6 text-center">
        <Key className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-semibold text-gray-900">
          No API keys
        </h3>
        <p className="mt-1 text-sm text-gray-500">
          You haven&apos;t created any API keys yet.
        </p>
      </div>
    );
  }

  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {apiKeys.map((apiKey) => (
            <TableRow key={apiKey.id}>
              <TableCell className="font-medium">{apiKey.name}</TableCell>
              <TableCell>
                {formatDistanceToNow(new Date(apiKey.createdAt), {
                  addSuffix: true,
                })}
              </TableCell>
              <TableCell className="text-right">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => confirmRevoke(apiKey.id)}
                  disabled={isRevoking}
                >
                  <Trash2 className="mr-1 h-4 w-4" />
                  Revoke
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={openDialog} onOpenChange={setOpenDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Revoke API Key</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to revoke this API key? This action cannot
              be undone and any applications using this key will no longer be
              able to authenticate.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isRevoking}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => selectedKeyId && handleRevoke(selectedKeyId)}
              disabled={isRevoking}
              className="bg-red-600 hover:bg-red-700"
            >
              {isRevoking ? 'Revoking...' : 'Revoke Key'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
