'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { showToast } from '@/components/ui/sonner';
import { Copy, Key } from 'lucide-react';
import { useState } from 'react';
import { createApiKeyAction } from '../actions';

export function CreateApiKeyForm() {
  const [isCreating, setIsCreating] = useState(false);
  const [name, setName] = useState('');
  const [showKeyDialog, setShowKeyDialog] = useState(false);
  const [newApiKey, setNewApiKey] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);
    setError('');

    const result = await createApiKeyAction({ name });

    if (result.error) {
      setError(result.error);
      showToast({ text: result.error, variant: 'error' });
    } else if (result.apiKey) {
      setNewApiKey(result.apiKey);
      setShowKeyDialog(true);
      setName('');
      showToast({ text: result.message, variant: 'success' });
    }

    setIsCreating(false);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(newApiKey);
    showToast({ text: 'API key copied to clipboard', variant: 'success' });
  };

  const closeDialog = () => {
    setShowKeyDialog(false);
    setNewApiKey('');
  };

  return (
    <>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">API Key Name</Label>
          <Input
            id="name"
            placeholder="e.g., Production API Key"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
          <p className="text-sm text-gray-500">
            Give your API key a descriptive name to identify its purpose.
          </p>
        </div>

        {error && <p className="text-sm text-red-500">{error}</p>}

        <Button type="submit" disabled={isCreating}>
          {isCreating ? 'Creating...' : 'Create API Key'}
        </Button>
      </form>

      <Dialog open={showKeyDialog} onOpenChange={closeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              Your New API Key
            </DialogTitle>
            <DialogDescription>
              This is your new API key. Please copy it now as you won&apos;t be
              able to see it again.
            </DialogDescription>
          </DialogHeader>

          <div className="my-4">
            <div className="relative rounded-md bg-gray-100 p-3 font-mono text-sm break-all">
              {newApiKey}
              <Button
                size="sm"
                variant="ghost"
                className="absolute top-2 right-2"
                onClick={copyToClipboard}
              >
                <Copy className="size-4" />
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button onClick={closeDialog}>I&apos;ve Copied My Key</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
