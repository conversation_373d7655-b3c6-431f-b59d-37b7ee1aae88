import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { getProjectForUser, getUser } from '@/lib/db/queries';
import { getProjectValidationRules } from '@/lib/db/rules';
import ValidationRulesForm from './validation-rules-form';

export default async function RulesPage() {
  // Get current user and project
  const user = await getUser();
  if (!user) {
    return <div className="p-6">You must be logged in to view rules.</div>;
  }
  const project = await getProjectForUser(user.id);
  if (!project) {
    return <div className="p-6">No project found for your account.</div>;
  }

  // Fetch validation rules
  const validationRules = await getProjectValidationRules(project.id);

  return (
    <>
      <header className="sticky top-0 z-10 border-b border-gray-200 bg-white px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <SidebarTrigger />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Validation Rules
              </h1>
              <p className="text-sm text-gray-600">
                Configure email validation checks for your project
              </p>
            </div>
          </div>
        </div>
      </header>
      <main className="space-y-6 p-6">
        {/* Stats Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {Object.values(validationRules).filter(Boolean).length}
                </div>
                <p className="text-sm text-gray-600">Active Validation Rules</p>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">
                  Only differences from defaults are stored
                </div>
                <div className="text-xs text-gray-500">
                  Optimized for efficiency
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Validation Rules Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">
              Email Validation Configuration
            </CardTitle>
            <p className="text-sm text-gray-600">
              Configure which validation checks to apply to incoming emails.
              Only rules that differ from defaults are stored for maximum
              efficiency.
            </p>
          </CardHeader>
          <CardContent>
            <ValidationRulesForm initialRules={validationRules} />
          </CardContent>
        </Card>

        {/* Information Card */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-6">
            <div className="space-y-3">
              <h3 className="font-medium text-blue-900">
                Default Validation Settings
              </h3>
              <div className="grid gap-3 text-sm text-blue-800 md:grid-cols-2">
                <div className="space-y-1">
                  <p>
                    <strong>✅ Enabled by default:</strong>
                  </p>
                  <ul className="ml-4 space-y-1 text-xs">
                    <li>• Check disposable emails</li>
                    <li>• Check MX records</li>
                    <li>• Check email syntax</li>
                    <li>• Check suspicious patterns</li>
                  </ul>
                </div>
                <div className="space-y-1">
                  <p>
                    <strong>❌ Disabled by default:</strong>
                  </p>
                  <ul className="ml-4 space-y-1 text-xs">
                    <li>• Check role-based accounts</li>
                  </ul>
                </div>
              </div>
              <p className="text-xs text-blue-700">
                Only enable role-based checking if you need to flag emails like
                admin@, info@, sales@ etc.
              </p>
            </div>
          </CardContent>
        </Card>
      </main>
    </>
  );
}
