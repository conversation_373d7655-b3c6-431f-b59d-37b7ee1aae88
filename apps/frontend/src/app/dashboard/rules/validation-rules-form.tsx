'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import type { ValidationRules } from '@/lib/db/rules';
import { useActionState, useState } from 'react';
import { updateValidationRules } from './actions';

interface ValidationRulesFormProps {
  initialRules: ValidationRules;
}

const ruleDescriptions = {
  checkDisposable: {
    title: 'Check Disposable Emails',
    description: 'Detect and flag temporary or disposable email services.',
    defaultValue: true,
  },
  checkForMxRecords: {
    title: 'Check MX Records',
    description: 'Verify that the email domain has valid MX DNS records.',
    defaultValue: true,
  },
  checkSyntax: {
    title: 'Check Email Syntax',
    description: 'Validate email format according to RFC standards.',
    defaultValue: true,
  },
  checkRoleBasedAccounts: {
    title: 'Check Role-Based Accounts',
    description: 'Flag role-based emails like admin@, info@, sales@.',
    defaultValue: false,
  },
  checkPatterns: {
    title: 'Check Suspicious Patterns',
    description: 'Detect suspicious patterns in email addresses.',
    defaultValue: true,
  },
} as const;

export default function ValidationRulesForm({
  initialRules,
}: ValidationRulesFormProps) {
  const [rules, setRules] = useState<ValidationRules>(initialRules);
  const [state, formAction, pending] = useActionState(
    updateValidationRules,
    null
  );

  const handleRuleChange = (ruleKey: keyof ValidationRules, value: boolean) => {
    setRules((prev) => ({
      ...prev,
      [ruleKey]: value,
    }));
  };

  const isRuleModified = (ruleKey: keyof ValidationRules) => {
    return rules[ruleKey] !== ruleDescriptions[ruleKey].defaultValue;
  };

  return (
    <form action={formAction} className="space-y-6">
      {/* Success/Error Messages */}
      {state?.success && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="text-sm text-green-700">{state.message}</div>
        </div>
      )}
      {state?.success === false && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{state.message}</div>
        </div>
      )}

      <div className="space-y-6">
        {Object.entries(ruleDescriptions).map(([key, config]) => {
          const ruleKey = key as keyof ValidationRules;
          const isModified = isRuleModified(ruleKey);

          return (
            <div key={key} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <Label htmlFor={key} className="text-sm font-medium">
                      {config.title}
                    </Label>
                    {isModified && (
                      <Badge variant="secondary" className="text-xs">
                        Modified
                      </Badge>
                    )}
                  </div>
                  <p className="mt-1 text-sm text-gray-600">
                    {config.description}
                  </p>
                  <p className="mt-1 text-xs text-gray-500">
                    Default: {config.defaultValue ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id={key}
                    checked={rules[ruleKey]}
                    onCheckedChange={(checked) =>
                      handleRuleChange(ruleKey, checked)
                    }
                    disabled={pending}
                  />
                  <input
                    type="hidden"
                    name={key}
                    value={rules[ruleKey].toString()}
                  />
                </div>
              </div>
              <Separator />
            </div>
          );
        })}
      </div>

      <div className="flex items-center justify-between pt-4">
        <div className="text-sm text-gray-600">
          Only rules that differ from defaults are saved to optimize storage.
        </div>
        <Button type="submit" disabled={pending} className="min-w-[120px]">
          {pending ? 'Saving...' : 'Save Rules'}
        </Button>
      </div>
    </form>
  );
}
