'use server';

import { getProjectForUser, getUser } from '@/lib/db/queries';
import {
  addRuleEntry,
  deleteRuleEntry,
  getProjectValidationRules,
  updateProjectValidationRules,
  type ValidationRules,
} from '@/lib/db/rules';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { z } from 'zod';

// Validation schema for rule entries
const ruleEntrySchema = z.object({
  type: z.enum(['EMAIL', 'DOMAIN']),
  value: z.string().min(1, 'Value is required'),
  listType: z.enum(['WHITELIST', 'BLACKLIST']),
});

// Validation schema for validation rules
const validationRulesSchema = z.object({
  checkDisposable: z.boolean().optional(),
  checkForMxRecords: z.boolean().optional(),
  checkSyntax: z.boolean().optional(),
  checkRoleBasedAccounts: z.boolean().optional(),
  checkPatterns: z.boolean().optional(),
});

export async function addRule(prevState: any, formData: FormData) {
  const user = await getUser();
  if (!user) {
    redirect('/sign-in');
  }

  const project = await getProjectForUser(user.id);
  if (!project) {
    throw new Error('Project not found');
  }

  try {
    const rawData = {
      type: formData.get('type') as string,
      value: formData.get('value') as string,
      listType: formData.get('listType') as string,
    };

    const validatedData = ruleEntrySchema.parse(rawData);

    await addRuleEntry({
      projectId: project.id,
      type: validatedData.type as 'EMAIL' | 'DOMAIN',
      value: validatedData.value,
      listType: validatedData.listType as 'WHITELIST' | 'BLACKLIST',
      userId: user.id,
    });

    revalidatePath('/dashboard/rules');
    return { success: true, message: 'Rule added successfully' };
  } catch (error) {
    console.error('Error adding rule:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.flatten().fieldErrors,
        message: 'Please check your input and try again',
      };
    }
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to add rule',
    };
  }
}

export async function deleteRule(ruleId: string) {
  const user = await getUser();
  if (!user) {
    redirect('/sign-in');
  }

  const project = await getProjectForUser(user.id);
  if (!project) {
    throw new Error('Project not found');
  }

  try {
    await deleteRuleEntry(ruleId, project.id, user.id);
    revalidatePath('/dashboard/rules');
    return { success: true, message: 'Rule deleted successfully' };
  } catch (error) {
    console.error('Error deleting rule:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to delete rule',
    };
  }
}

export async function updateValidationRules(
  prevState: any,
  formData: FormData
) {
  const user = await getUser();
  if (!user) {
    redirect('/sign-in');
  }

  const project = await getProjectForUser(user.id);
  if (!project) {
    throw new Error('Project not found');
  }

  try {
    // Proper boolean parsing for FormData values
    // FormData.get() returns string "true"/"false" from hidden inputs
    // or null if not present
    const parseFormBoolean = (value: FormDataEntryValue | null): boolean => {
      if (value === null) return false;
      return value.toString() === 'true';
    };

    const rawData = {
      checkDisposable: parseFormBoolean(formData.get('checkDisposable')),
      checkForMxRecords: parseFormBoolean(formData.get('checkForMxRecords')),
      checkSyntax: parseFormBoolean(formData.get('checkSyntax')),
      checkRoleBasedAccounts: parseFormBoolean(
        formData.get('checkRoleBasedAccounts')
      ),
      checkPatterns: parseFormBoolean(formData.get('checkPatterns')),
    };

    const validatedData = validationRulesSchema.parse(rawData);

    await updateProjectValidationRules(project.id, validatedData, user.id);

    revalidatePath('/dashboard/rules');
    return { success: true, message: 'Validation rules updated successfully' };
  } catch (error) {
    console.error('Error updating validation rules:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.flatten().fieldErrors,
        message: 'Please check your input and try again',
      };
    }
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : 'Failed to update validation rules',
    };
  }
}

export async function getCurrentValidationRules(): Promise<ValidationRules> {
  const user = await getUser();
  if (!user) {
    redirect('/sign-in');
  }

  const project = await getProjectForUser(user.id);
  if (!project) {
    throw new Error('Project not found');
  }

  return await getProjectValidationRules(project.id);
}
