import { getRecentApiUsage } from '@/lib/db/api-usage';
import { getProjectForUser, getUser } from '@/lib/db/queries';
import {
  consumeConcurrencyLimit,
  consumeRateLimit,
  releaseConcurrency,
} from '@/lib/limiter';
import { getDateRangeForTimeframe } from '@/utils/timeframe';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const user = await getUser();
  if (!user) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }
  const key = user.id || req.headers.get('x-forwarded-for') || 'anonymous';
  try {
    await consumeRateLimit(key);
  } catch {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }
  try {
    await consumeConcurrencyLimit(key);
  } catch {
    return NextResponse.json(
      { error: 'Too many concurrent requests. Please try again later.' },
      { status: 429 }
    );
  }
  const project = await getProjectForUser(user.id);
  if (!project) {
    return NextResponse.json({ error: 'No project found' }, { status: 404 });
  }
  const { searchParams } = new URL(req.url);
  const page = parseInt(searchParams.get('page') || '1', 10);
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const action = searchParams.get('action');
  const timeframe = searchParams.get('timeframe') || 'today';
  const { from, to } = getDateRangeForTimeframe(timeframe);

  // Build filter
  let actionFilter: string[] | undefined = undefined;
  if (action === 'deny-quarantine') {
    actionFilter = ['DENY', 'QUARANTINE'];
  } else if (action) {
    actionFilter = [action.toUpperCase()];
  }

  const result = await getRecentApiUsage(project.id, page, pageSize, {
    action: actionFilter,
    from,
    to,
  });
  await releaseConcurrency(key);
  return NextResponse.json(result);
}
