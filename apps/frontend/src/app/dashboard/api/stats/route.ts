import { getApiUsageStats, getRecentApiUsage } from '@/lib/db/api-usage';
import { getProjectForUser, getUser } from '@/lib/db/queries';
import {
  consumeConcurrencyLimit,
  consumeRateLimit,
  releaseConcurrency,
} from '@/lib/limiter';
import { getDateRangeForTimeframe } from '@/utils/timeframe';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(req: NextRequest) {
  const user = await getUser();
  if (!user) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }
  // Use user.id or IP as the key
  const key = user.id || req.headers.get('x-forwarded-for') || 'anonymous';
  try {
    await consumeRateLimit(key);
  } catch {
    return NextResponse.json(
      { error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }
  try {
    await consumeConcurrencyLimit(key);
  } catch {
    return NextResponse.json(
      { error: 'Too many concurrent requests. Please try again later.' },
      { status: 429 }
    );
  }
  const project = await getProjectForUser(user.id);
  if (!project) {
    return NextResponse.json({ error: 'No project found' }, { status: 404 });
  }
  const { searchParams } = new URL(req.url);
  const timeframe = searchParams.get('timeframe') || 'today';
  const { from, to } = getDateRangeForTimeframe(timeframe);

  // Get stats
  const stats = await getApiUsageStats(project.id, { from, to });
  // Get chart data (group by hour for now)
  const chartResult = await getRecentApiUsage(project.id, 1, 500, { from, to });
  // Group by hour
  const chartData = Array(24)
    .fill(0)
    .map((_, i) => ({ hour: i, allow: 0, deny: 0, quarantine: 0 }));
  for (const rec of chartResult.data) {
    const d = new Date(rec.createdAt);
    const hour = d.getHours();
    if (rec.action === 'ALLOW') chartData[hour].allow++;
    if (rec.action === 'DENY') chartData[hour].deny++;
    if (rec.action === 'QUARANTINE') chartData[hour].quarantine++;
  }
  // Calculate success rate
  const total = stats.allowCount + stats.denyCount + stats.quarantineCount;
  const successRate = total > 0 ? (stats.allowCount / total) * 100 : 0;
  // At the end, release concurrency slot
  await releaseConcurrency(key);
  return NextResponse.json({
    usersDenied: stats.denyCount,
    checksDone: total,
    apiCalls: total,
    successRate,
    chartData,
  });
}
