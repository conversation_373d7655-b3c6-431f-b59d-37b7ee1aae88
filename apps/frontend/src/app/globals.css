@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-roboto: var(--font-roboto);
  --font-ibm-plex-mono: var(--font-ibm-plex-mono);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: #fafafa;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #8b5cf6;
  --secondary-foreground: #ffffff;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --accent: #eef2ff;
  --accent-foreground: #4f46e5;
  --destructive: #ef4444;
  --border: #e4e4e7;
  --input: #e4e4e7;
  --ring: #a5b4fc;
  --chart-1: #6366f1;
  --chart-2: #3b82f6;
  --chart-3: #ec4899;
  --chart-4: #8b5cf6;
  --chart-5: #06b6d4;
  --sidebar: #f4f4f5;
  --sidebar-foreground: #171717;
  --sidebar-primary: #4f46e5;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #eef2ff;
  --sidebar-accent-foreground: #4f46e5;
  --sidebar-border: #e4e4e7;
  --sidebar-ring: #a5b4fc;
  --info: #3b82f6;
  --success: #10b981;
  --warning: #f59e0b;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ffffff;
  --card: #171717;
  --card-foreground: #ffffff;
  --popover: #171717;
  --popover-foreground: #ffffff;
  --primary: #6366f1;
  --primary-foreground: #ffffff;
  --secondary: #8b5cf6;
  --secondary-foreground: #ffffff;
  --muted: #262626;
  --muted-foreground: #a3a3a3;
  --accent: #2e1065;
  --accent-foreground: #c4b5fd;
  --destructive: #dc2626;
  --border: #404040;
  --input: #404040;
  --ring: #4f46e5;
  --chart-1: #6366f1;
  --chart-2: #3b82f6;
  --chart-3: #ec4899;
  --chart-4: #8b5cf6;
  --chart-5: #06b6d4;
  --sidebar: #171717;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #6366f1;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #2e1065;
  --sidebar-accent-foreground: #c4b5fd;
  --sidebar-border: #404040;
  --sidebar-ring: #4f46e5;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  h1,
  h2,
  h3,
  h4 {
    @apply font-bold tracking-tight;
  }

  h1 {
    @apply text-2xl leading-tight sm:text-3xl md:text-4xl;
  }

  h2 {
    @apply text-xl leading-tight sm:text-2xl md:text-3xl;
  }

  h3 {
    @apply text-lg leading-snug sm:text-xl md:text-2xl;
  }

  h4 {
    @apply text-base sm:text-lg md:text-xl;
  }
}
