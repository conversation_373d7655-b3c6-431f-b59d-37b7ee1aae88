import { z } from "zod";
// for server side validation
export const SignupSchema = z.object({
  name: z.string().min(3, {
    message: "Name must be at least 3 characters.",
  }),
  email: z.string().email({
    message: "Invalid email address.",
  }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters." })
    .max(20, { message: "Password must be at most 20 characters." }),
});


// for client side validation
export const SignupFormSchema = SignupSchema.extend({
  confirmPassword: z
    .string()
    .min(1, { message: "Confirm Password is required." }),
}).superRefine((values, ctx) => {
  if (values.password !== values.confirmPassword) {
    ctx.addIssue({
      code: "custom",
      message: "Passwords do not match",
      path: ["confirmPassword"],
    });
  }
  if (values.email && !z.string().email().safeParse(values.email).success) {
    ctx.addIssue({
      code: "custom",
      message: "Please enter a valid email address",
      path: ["email"],
    });
  }
});

export type SignupFormValues = z.infer<typeof SignupFormSchema>;
export type SignupValues = z.infer<typeof SignupSchema>;

export const LoginSchema = z.object({
  email: z.string().email({ message: "Invalid email address." }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters." })
    .max(20, { message: "Password must be at most 20 characters." }),
});

export type LoginValues = z.infer<typeof LoginSchema>;
