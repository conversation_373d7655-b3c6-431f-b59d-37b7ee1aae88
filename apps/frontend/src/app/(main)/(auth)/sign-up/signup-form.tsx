'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import PasswordInput from '@/components/ui/password-input';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { signupAction } from '../actions';
import { SignupFormSchema, type SignupFormValues } from '../schema';

export default function SignupForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState('');

  const form = useForm<SignupFormValues>({
    resolver: zodResolver(SignupFormSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  const router = useRouter();

  async function onSubmit(data: SignupFormValues) {
    try {
      setIsLoading(true);
      setIsError('');
      const { name, email, password } = data;
      const result = await signupAction({ name, email, password });

      if (!result.success) {
        if (result.errors) {
          result.errors.forEach((error) => {
            form.setError(error.field as keyof SignupFormValues, {
              type: 'manual',
              message: error.message,
            });
          });
        }
        if (result.message) {
          setIsError(result.message);
        }
        return;
      }
      router.push('/sign-in');
    } catch (error) {
      console.error('Signup error:', error);
      setIsError('An error occurred during signup.');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Fullname</FormLabel>
              <FormControl>
                <Input placeholder="Enter your fullname" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Email</FormLabel>
              <FormControl>
                <Input placeholder="Enter your email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Password</FormLabel>
              <FormControl>
                <PasswordInput placeholder="Enter password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel required>Confirm Password</FormLabel>
              <FormControl>
                <PasswordInput {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {isError && <p>{isError}</p>}
        <Button loading={isLoading} className="w-full uppercase" type="submit">
          Sign Up
        </Button>
      </form>
    </Form>
  );
}
