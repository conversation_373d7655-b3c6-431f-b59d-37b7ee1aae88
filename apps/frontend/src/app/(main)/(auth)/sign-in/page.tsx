import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { LoginForm } from "./login-form";

export const metadata: Metadata = {
  title: "Sign In",
};

export default function LoginPage() {
  return (
    <>
      <h2 className="text-center">Login</h2>
      <LoginForm />
      <div className="flex items-center justify-between">
        <p className="text-muted-foreground text-sm">
          Don&apos;t have an account?{" "}
          <Link href="/sign-up" className="text-blue-500">
            Sign up
          </Link>
          .
        </p>
      </div>
    </>
  );
}
