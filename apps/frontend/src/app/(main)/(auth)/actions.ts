'use server';

import { auth } from '@/lib/auth';
import { createActivity } from '@/lib/db';
import { createProject } from '@/lib/db/project';
import prisma from '@/lib/prisma';
import { ActivityType } from 'database';
import { isRedirectError } from 'next/dist/client/components/redirect-error';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import {
  LoginSchema,
  SignupSchema,
  type LoginValues,
  type SignupValues,
} from './schema';

export const signupAction = async (data: SignupValues) => {
  try {
    const validation = SignupSchema.safeParse(data);
    if (!validation.success) {
      return {
        success: false,
        errors: validation.error.issues.map((issue) => ({
          field: issue.path[0],
          message: issue.message,
        })),
      };
    }
    const { name, email, password } = validation.data;

    const existingUser = await prisma.user.findUnique({
      where: {
        email: email,
      },
    });

    if (existingUser) {
      return {
        success: false,
        errors: [
          {
            field: 'email',
            message: 'User already exists with this email.',
          },
        ],
      };
    }

    const freePlan = await prisma.plan.findUnique({
      where: { name: 'Free' },
    });

    if (!freePlan) {
      return {
        success: false,
        errors: [
          { field: 'email', message: 'Something went wrong, Contact support' },
        ],
      };
    }

    const user = await auth.api.signUpEmail({
      body: {
        email,
        password,
        name,
        planId: freePlan.id,
      },
    });

    await createActivity({
      userId: user.user.id,
      action: ActivityType.SIGN_UP,
    });

    await createProject({
      userId: user.user.id,
    });

    return {
      success: true,
      errors: [],
      message: 'Registration successful',
    };
  } catch (error) {
    console.error('Signup error:', error);
    return {
      success: false,
      errors: [],
      message: 'An unexpected error occurred',
    };
  }
};

export const signinAction = async (data: LoginValues) => {
  try {
    const validation = LoginSchema.safeParse(data);
    if (!validation.success) {
      return {
        success: false,
        error: validation.error.format(),
      };
    }
    const { email, password } = validation.data;

    const userData = await auth.api.signInEmail({
      body: {
        email,
        password,
      },
    });

    await createActivity({
      userId: userData.user.id,
      action: ActivityType.SIGN_IN,
    });

    return {
      success: true,
      message: 'Signin successful',
    };
  } catch (error) {
    if (error instanceof Error && !isRedirectError(error)) {
      console.error('Signin error:', error.cause);
      return {
        success: false,
        error: error.message,
      };
    }
    throw error;
  }
};

export const signoutAction = async () => {
  try {
    await auth.api.signOut({
      headers: await headers(),
    });
    redirect('/');
  } catch (error) {
    console.error('Signout error:', error);
  }
};
