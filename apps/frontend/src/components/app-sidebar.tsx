'use client';

import { Ava<PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Progress } from '@/components/ui/progress';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import { signOut, useSession } from '@/lib/auth-client';
import {
  BarChart3,
  Bell,
  ChevronUp,
  Crown,
  Filter,
  Key,
  LogOut,
  Settings,
  Shield,
  User2,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useRefetchTrigger } from './refetch-context';

const navigation = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: BarChart3,
  },
  // {
  //   title: 'Check Email',
  //   url: '/dashboard/check-email',
  //   icon: Mail,
  // },
  {
    title: 'Rules',
    url: '/dashboard/rules',
    icon: Shield,
  },
  {
    title: 'Whitelist/Blacklist',
    url: '/dashboard/lists',
    icon: Filter,
  },
];

const settings = [
  {
    title: 'API Keys',
    url: '/dashboard/api-keys',
    icon: Key,
  },
  // {
  //   title: 'Credits',
  //   url: '/dashboard/credits',
  //   icon: CreditCard,
  // },
  {
    title: 'Account Settings',
    url: '/dashboard/settings',
    icon: Settings,
  },
  {
    title: 'Pricing',
    url: '/dashboard/pricing',
    icon: Crown,
  },
];

export default function AppSidebar() {
  const pathname = usePathname();
  const { data: session, refetch } = useSession();
  const { refetchSignal } = useRefetchTrigger();

  const router = useRouter();

  const user = session?.user;
  const credits = user?.credits ?? 0;
  const plan = user?.planId
    ? user.planId === 'premium'
      ? 'Premium'
      : 'Free'
    : 'Free';
  // For demo, assume free plan has 200 credits
  const maxCredits = 200;
  const usedCredits = maxCredits - credits;
  const percentUsed = maxCredits > 0 ? (usedCredits / maxCredits) * 100 : 0;

  // Refetch session/user data when refetchSignal changes
  useEffect(() => {
    refetch();
  }, [refetchSignal, refetch]);

  const handleSignOut = () => {
    signOut();
    refetch();
    router.refresh();
    router.push('/sign-in');
  };

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <div className="flex items-center space-x-2 px-2 py-1">
          <Shield className="size-8 shrink-0 text-purple-600 group-data-[collapsible=icon]:size-6" />
          <span className="text-xl font-bold text-gray-900 group-data-[collapsible=icon]:hidden">
            EmailGuard
          </span>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigation.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={pathname === item.url}>
                    <Link className="flex w-full items-center" href={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {settings.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild isActive={pathname === item.url}>
                    <Link className="flex w-full items-center" href={item.url}>
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {/* Credits Progress Card */}
        <SidebarGroup>
          <SidebarGroupContent>
            <Card className="mx-2 group-data-[collapsible=icon]:hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">
                  Credits Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>Used</span>
                    <span>
                      {usedCredits} / {maxCredits}
                    </span>
                  </div>
                  <Progress value={percentUsed} className="h-2" />
                </div>
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="text-xs">
                    {plan}
                  </Badge>
                  <span className="text-muted-foreground text-xs">
                    {credits} left
                  </span>
                </div>
              </CardContent>
            </Card>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarFallback className="rounded-lg bg-purple-600 text-white">
                      {user?.name?.charAt(0).toUpperCase() ||
                        user?.email?.charAt(0).toUpperCase() ||
                        'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">
                      {user?.name || 'User'}
                    </span>
                    <span className="truncate text-xs">
                      {user?.email || ''}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarFallback className="rounded-lg bg-purple-600 text-white">
                        {user?.name?.charAt(0).toUpperCase() ||
                          user?.email?.charAt(0).toUpperCase() ||
                          'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">John Doe</span>
                      <span className="truncate text-xs"><EMAIL></span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/settings">
                    <User2 className="mr-2 h-4 w-4" />
                    Account Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/pricing">
                    <Crown className="mr-2 h-4 w-4" />
                    Manage Subscription
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="cursor-pointer text-red-600"
                  onClick={handleSignOut}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Log out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
