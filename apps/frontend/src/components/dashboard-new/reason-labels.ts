export interface ReasonLabel {
  label: string;
  description: string;
  className: string;
}

export const REASON_LABELS: Record<string, ReasonLabel> = {
  // User whitelist reasons
  user_whitelisted_domain: {
    label: 'User whitelisted domain',
    description: 'The domain is explicitly allowed in the user whitelist.',
    className: 'text-green-600',
  },
  user_whitelisted_email: {
    label: 'User whitelisted email',
    description:
      'This specific email address is explicitly allowed in the user whitelist.',
    className: 'text-green-600',
  },

  // User blacklist reasons
  user_blacklisted_domain: {
    label: 'User blacklisted domain',
    description: 'The email domain is blacklisted by the user.',
    className: 'text-red-600',
  },
  user_blacklisted_email: {
    label: 'User blacklisted email',
    description: 'This specific email address is blacklisted by the user.',
    className: 'text-red-600',
  },

  // Disposable email reasons
  disposable_email_service: {
    label: 'Disposable email service',
    description:
      'Email uses a known temporary/disposable email service that allows users to create short-term email addresses.',
    className: 'text-yellow-600',
  },

  // MX record reasons
  mx_records_not_found: {
    label: 'No MX records found',
    description:
      'The domain does not have valid MX records and cannot receive emails.',
    className: 'text-red-600',
  },

  // Syntax reasons
  invalid_syntax: {
    label: 'Invalid email format',
    description:
      'The email address does not conform to standard formatting rules.',
    className: 'text-red-600',
  },

  // Role-based account reasons
  role_based_account: {
    label: 'Role-based account',
    description:
      'Email appears to be a role-based account (e.g., admin, info, support) rather than a personal email.',
    className: 'text-orange-600',
  },

  // Pattern matching reasons
  suspicious_pattern: {
    label: 'Suspicious pattern detected',
    description:
      'Email address contains patterns associated with fraudulent or suspicious activity.',
    className: 'text-orange-600',
  },

  // Error reasons
  validation_error: {
    label: 'Validation error',
    description: 'An error occurred during the validation process.',
    className: 'text-red-600',
  },

  // Legacy/backward compatibility reasons (for existing data)
  whitelisted_domain: {
    label: 'Whitelisted domain',
    description: 'The domain is explicitly allowed (legacy reason).',
    className: 'text-green-600',
  },
  blacklisted_domain: {
    label: 'Blacklisted domain',
    description: 'The email domain is blacklisted (legacy reason).',
    className: 'text-red-600',
  },
  temporary_email_domain: {
    label: 'Temporary email domain',
    description:
      'Email uses a known temporary/disposable email service (legacy reason).',
    className: 'text-yellow-600',
  },
  domain_blacklisted: {
    label: 'Blacklisted domain',
    description: 'The email domain is blacklisted (legacy reason).',
    className: 'text-red-600',
  },
  email_blacklisted: {
    label: 'Blacklisted email',
    description: 'This email address is blacklisted (legacy reason).',
    className: 'text-red-600',
  },
  whitelisted: {
    label: 'Whitelisted',
    description: 'This email address is explicitly allowed (legacy reason).',
    className: 'text-green-600',
  },
  domain_whitelisted: {
    label: 'Whitelisted domain',
    description: 'The domain is explicitly allowed (legacy reason).',
    className: 'text-green-600',
  },
  email_whitelisted: {
    label: 'Whitelisted email',
    description: 'This email address is explicitly allowed (legacy reason).',
    className: 'text-green-600',
  },
  high_risk: {
    label: 'High risk email',
    description:
      'Email address shows patterns associated with high risk based on risk score (legacy reason).',
    className: 'text-orange-600',
  },
  invalid_format: {
    label: 'Invalid email address format',
    description:
      'The email address does not conform to standard formatting rules (legacy reason).',
    className: 'text-red-600',
  },
  no_mx_record: {
    label: 'Email domain does not accept messages',
    description:
      'The domain does not have valid MX records and cannot receive emails (legacy reason).',
    className: 'text-red-600',
  },
  spam_blacklisted: {
    label: 'Blacklisted spam',
    description:
      'Email address has been flagged for sending spam or unwanted communications (legacy reason).',
    className: 'text-red-600',
  },
  abuse_reports: {
    label: 'Appears in spam or abuse reports',
    description:
      'This email address or domain has been reported for spam or abuse (legacy reason).',
    className: 'text-orange-600',
  },
  frequent_reports: {
    label: 'Reported frequently for abuse',
    description:
      'This email address or domain has been reported multiple times for abuse (legacy reason).',
    className: 'text-orange-600',
  },
  high_spam_confidence: {
    label: 'High confidence in spam reports',
    description:
      'There is a high confidence that this email is associated with spam (legacy reason).',
    className: 'text-red-600',
  },
  invalid_ip: {
    label: 'Invalid IP address',
    description:
      'The IP address of the domain is invalid or does not exist (legacy reason).',
    className: 'text-red-600',
  },
  not_valid: {
    label: 'Email address does not appear to be valid',
    description:
      'The email address failed validation checks and does not appear to be valid (legacy reason).',
    className: 'text-red-600',
  },
  trusted: {
    label: 'Trusted email address',
    description: 'This email address is recognized as trusted (legacy reason).',
    className: 'text-green-600',
  },
};
