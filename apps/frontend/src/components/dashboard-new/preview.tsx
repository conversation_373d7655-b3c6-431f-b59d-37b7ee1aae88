import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';
import { formatDistanceToNow } from 'date-fns';
import { AlertTriangle, Clock, Mail } from 'lucide-react';
import { REASON_LABELS } from './reason-labels';

// UsageRecord type should match the one in verification-history-table.tsx
export interface UsageRecord {
  id: string;
  createdAt: string;
  email: string;
  action: string;
  riskScore: number;
  reasons: string[];
  ipAddress?: string;
  apiKey?: { name?: string; key?: string };
}

interface VerificationPreviewSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  record: UsageRecord | null;
}

function getRiskScoreColor(score: number) {
  if (score >= 80) return 'text-destructive';
  if (score >= 60) return 'text-orange-600';
  if (score >= 40) return 'text-yellow-600';
  return 'text-green-600';
}

function getRiskLevel(score: number) {
  if (score >= 80) return 'High Risk';
  if (score >= 60) return 'Medium Risk';
  if (score >= 40) return 'Low Risk';
  return 'Safe';
}

export default function VerificationPreviewSheet({
  open,
  onOpenChange,
  record,
}: VerificationPreviewSheetProps) {
  if (!record) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full overflow-y-auto p-4 sm:max-w-lg">
        <SheetHeader>
          <SheetTitle className="flex items-center space-x-2 text-xl">
            <Mail className="h-5 w-5" />
            <span>Verification Details</span>
          </SheetTitle>
        </SheetHeader>

        <div className="mt-6 space-y-6">
          {/* Result Overview */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Risk Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <div className="relative h-16 w-16">
                    <svg
                      className="h-16 w-16 -rotate-90 transform"
                      viewBox="0 0 36 36"
                    >
                      <path
                        d="M18 2.0845
                          a 15.9155 15.9155 0 0 1 0 31.831
                          a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#e5e7eb"
                        strokeWidth="2"
                      />
                      <path
                        d="M18 2.0845
                          a 15.9155 15.9155 0 0 1 0 31.831
                          a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeDasharray={`${record.riskScore}, 100`}
                        className={getRiskScoreColor(record.riskScore)}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span
                        className={`text-xl font-bold ${getRiskScoreColor(record.riskScore)}`}
                      >
                        {record.riskScore}
                      </span>
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">
                      {getRiskLevel(record.riskScore)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Result
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  {record.action === 'DENY' ||
                  record.action === 'QUARANTINE' ? (
                    <AlertTriangle className="text-destructive h-5 w-5" />
                  ) : null}
                  <Badge
                    variant={
                      record.action === 'ALLOW'
                        ? 'success'
                        : record.action === 'DENY'
                          ? 'destructive'
                          : record.action === 'QUARANTINE'
                            ? 'secondary'
                            : 'default'
                    }
                    className="text-sm"
                  >
                    {record.action === 'ALLOW'
                      ? 'Good'
                      : record.action === 'DENY'
                        ? 'Blocked'
                        : record.action === 'QUARANTINE'
                          ? 'Quarantine'
                          : record.action}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Email Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Email Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Email Address</span>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="rounded bg-gray-100 px-2 py-1 font-mono text-sm">
                    {record.email}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Risk Score</span>
                <span
                  className={`text-sm font-semibold ${getRiskScoreColor(record.riskScore)}`}
                >
                  {record.riskScore}/100
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Timestamp */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
                <Clock className="h-5 w-5" />
                <span>Verification Time</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p
                className="text-sm text-gray-700"
                title={
                  record.createdAt
                    ? new Date(record.createdAt).toLocaleString()
                    : '-'
                }
              >
                {record.createdAt
                  ? formatDistanceToNow(new Date(record.createdAt), {
                      addSuffix: true,
                    })
                  : '-'}
              </p>
            </CardContent>
          </Card>

          {/* Detection Results */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold">
                Detection Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {record.reasons && record.reasons.length > 0 ? (
                  record.reasons.map((reason, index) => {
                    const info = REASON_LABELS[reason] || {
                      label: reason,
                      description: 'No description available.',
                      className: 'text-gray-600',
                    };
                    return (
                      <div
                        key={index}
                        className="flex items-start justify-between rounded-lg bg-gray-50 p-3"
                      >
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span
                              className={`text-sm font-medium ${info.className}`}
                            >
                              {info.label}
                            </span>
                            <Badge
                              variant={
                                reason === 'whitelisted' ||
                                reason === 'domain_whitelisted' ||
                                reason === 'email_whitelisted'
                                  ? 'success'
                                  : reason === 'high_risk'
                                    ? 'secondary'
                                    : 'destructive'
                              }
                              className="text-xs"
                            >
                              {info.label}
                            </Badge>
                          </div>
                          <p className="mt-1 text-xs text-gray-600">
                            {info.description}
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            Code: {reason}
                          </p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <span className="text-xs text-gray-400">
                    No signals detected.
                  </span>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Button */}
          {/*
          <Button className="w-full bg-green-600 hover:bg-green-700">
            <ExternalLink className="mr-2 h-4 w-4" />
            View Detailed Report
          </Button>
          */}
        </div>
      </SheetContent>
    </Sheet>
  );
}
