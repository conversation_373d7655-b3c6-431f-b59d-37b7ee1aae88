'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { QueryFunctionContext, useQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { Eye } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useRefetchTrigger } from '../refetch-context';
import VerificationPreviewSheet from './preview';
import { REASON_LABELS } from './reason-labels';

interface UsageRecord {
  id: string;
  createdAt: string;
  email: string;
  action: string;
  riskScore: number;
  reasons: string[];
  ipAddress?: string;
  apiKey?: { name?: string; key?: string };
}

type FilterType = 'all' | 'deny-quarantine';

function getReasonLabel(reason: string) {
  const entry = REASON_LABELS[reason];
  if (entry)
    return <span className={`text-xs ${entry.className}`}>{entry.label}</span>;
  return <span className="text-xs text-gray-600">{reason}</span>;
}

function getActionBadge(action: string) {
  switch (action) {
    case 'ALLOW':
      return <Badge variant="success">Good</Badge>;
    case 'DENY':
      return <Badge variant="destructive">Blocked</Badge>;
    case 'QUARANTINE':
      return <Badge variant="secondary">Quarantine</Badge>;
    default:
      return <Badge variant="default">{action}</Badge>;
  }
}

export function VerificationHistoryTable({ timeframe }: { timeframe: string }) {
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState<FilterType>('all');
  const { refetchSignal } = useRefetchTrigger();
  const [previewOpen, setPreviewOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<UsageRecord | null>(
    null
  );

  const fetchHistory = useCallback(
    async ({
      queryKey,
    }: QueryFunctionContext<
      readonly [
        string,
        { page: number; filter: string; timeframe: string },
        number?,
      ]
    >) => {
      const [_key, { page, filter, timeframe }] = queryKey;
      const params = new URLSearchParams({ page: String(page), timeframe });
      if (filter === 'deny-quarantine')
        params.append('action', 'deny-quarantine');
      const res = await fetch(`/dashboard/api/validation-history?${params}`);
      if (!res.ok)
        throw new Error((await res.json()).error || 'Failed to fetch history');
      return res.json();
    },
    []
  );

  const {
    data = { data: [], totalPages: 1, total: 0 },
    isPending,
    error,
    refetch,
  } = useQuery<
    { data: UsageRecord[]; totalPages: number; total: number },
    Error,
    { data: UsageRecord[]; totalPages: number; total: number },
    readonly [
      string,
      { page: number; filter: string; timeframe: string },
      number?,
    ]
  >({
    queryKey: [
      'validation-history',
      { page, filter, timeframe },
      refetchSignal,
    ],
    queryFn: fetchHistory,
  });

  useEffect(() => {
    refetch();
  }, [refetchSignal, refetch]);

  const totalPages = data.totalPages;
  const records = data.data;

  const handleSetFilter = useCallback((newFilter: FilterType) => {
    setFilter(newFilter);
    setPage(1);
  }, []);

  const renderTableBody = useMemo(() => {
    if (isPending) {
      // Show 10 skeleton rows
      return Array.from({ length: 10 }).map((_, idx) => (
        <TableRow key={`skeleton-${idx}`}>
          <TableCell>
            <Skeleton className="h-4 w-24" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-40" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-32" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-20 rounded" />
          </TableCell>
        </TableRow>
      ));
    }
    if (error) {
      return (
        <TableRow>
          <TableCell colSpan={5} className="text-center text-red-500">
            {error.message}
          </TableCell>
        </TableRow>
      );
    }
    if (records.length === 0) {
      return (
        <TableRow>
          <TableCell colSpan={5} className="text-center text-gray-500">
            No validation history found.
          </TableCell>
        </TableRow>
      );
    }
    return records.map((record: UsageRecord) => (
      <TableRow key={record.id}>
        <TableCell>
          {formatDistanceToNow(new Date(record.createdAt), { addSuffix: true })}
        </TableCell>
        <TableCell>{getActionBadge(record.action)}</TableCell>
        <TableCell>
          <span className="font-mono text-sm">{record.email}</span>
        </TableCell>
        <TableCell>
          <div className="space-y-1">
            {record.reasons && record.reasons.length > 0 ? (
              getReasonLabel(record.reasons[0])
            ) : (
              <span className="text-xs text-gray-400">-</span>
            )}
          </div>
        </TableCell>
        <TableCell>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSelectedRecord(record);
              setPreviewOpen(true);
            }}
          >
            <Eye className="mr-1 h-4 w-4" />
            Preview
          </Button>
        </TableCell>
      </TableRow>
    ));
  }, [isPending, error, records]);

  return (
    <div className="space-y-4">
      <div className="mb-2 flex items-center gap-2">
        <Button
          variant={filter === 'all' ? 'secondary' : 'outline'}
          size="sm"
          onClick={() => handleSetFilter('all')}
        >
          All verifications
        </Button>
        <Button
          variant={filter === 'deny-quarantine' ? 'secondary' : 'outline'}
          size="sm"
          onClick={() => handleSetFilter('deny-quarantine')}
        >
          Deny / Quarantine
        </Button>
      </div>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Result</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Signals</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>{renderTableBody}</TableBody>
      </Table>
      {/* Pagination */}
      <div className="mt-4 flex flex-wrap items-center justify-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage((p) => Math.max(1, p - 1))}
          disabled={page === 1}
        >
          Previous
        </Button>
        {/* Page numbers */}
        {Array.from({ length: totalPages }, (_, i) => i + 1)
          .filter((p) => {
            // Always show first, last, current, and neighbors
            if (totalPages <= 7) return true;
            if (p === 1 || p === totalPages) return true;
            if (Math.abs(p - page) <= 1) return true;
            if (page <= 4 && p <= 5) return true;
            if (page >= totalPages - 3 && p >= totalPages - 4) return true;
            return false;
          })
          .map((p, idx, arr) => {
            // Add ellipsis if needed
            if (idx > 0 && p - arr[idx - 1] > 1) {
              return [
                <span key={`ellipsis-${p}`} className="px-1 text-gray-400">
                  ...
                </span>,
                <Button
                  key={p}
                  variant={p === page ? 'secondary' : 'outline'}
                  size="sm"
                  onClick={() => setPage(p)}
                  aria-current={p === page ? 'page' : undefined}
                >
                  {p}
                </Button>,
              ];
            }
            return (
              <Button
                key={p}
                variant={p === page ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setPage(p)}
                aria-current={p === page ? 'page' : undefined}
              >
                {p}
              </Button>
            );
          })}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
          disabled={page === totalPages}
        >
          Next
        </Button>
      </div>
      {/* Preview Sheet */}
      <VerificationPreviewSheet
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        record={selectedRecord}
      />
    </div>
  );
}
