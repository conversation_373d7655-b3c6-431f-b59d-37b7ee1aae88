'use client';

import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>u, Shield } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export default function LandingHeader() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="fixed top-0 z-50 w-full border-b border-gray-200 bg-white/80 backdrop-blur-lg">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div>
            <Link href="/" className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-purple-600 to-blue-600">
                <Shield className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">
                EmailGuard
              </span>
            </Link>
          </div>

          <nav className="hidden items-center space-x-8 md:flex">
            <Link
              href="#features"
              className="text-gray-600 transition-colors hover:text-purple-600"
            >
              Features
            </Link>
            <Link
              href="#use-cases"
              className="text-gray-600 transition-colors hover:text-purple-600"
            >
              Use Cases
            </Link>
            <Link
              href="#documentation"
              className="text-gray-600 transition-colors hover:text-purple-600"
            >
              Documentation
            </Link>
            <Link
              href="#pricing"
              className="text-gray-600 transition-colors hover:text-purple-600"
            >
              Pricing
            </Link>
          </nav>

          <div className="hidden items-center space-x-4 md:flex">
            <Button variant="ghost" asChild>
              <Link href="/sign-in">Sign In</Link>
            </Button>
            <Button
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              asChild
            >
              <Link href="/sign-up">Try It Free</Link>
            </Button>
          </div>

          <button
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>

        {isMenuOpen && (
          <div className="mt-4 border-t border-gray-200 py-4 md:hidden">
            <nav className="flex flex-col space-y-4">
              <Link
                href="#features"
                className="text-gray-600 transition-colors hover:text-purple-600"
              >
                Features
              </Link>
              <Link
                href="#use-cases"
                className="text-gray-600 transition-colors hover:text-purple-600"
              >
                Use Cases
              </Link>
              <Link
                href="#documentation"
                className="text-gray-600 transition-colors hover:text-purple-600"
              >
                Documentation
              </Link>
              <Link
                href="#pricing"
                className="text-gray-600 transition-colors hover:text-purple-600"
              >
                Pricing
              </Link>
              <div className="flex flex-col space-y-2 pt-4">
                <Button variant="ghost" asChild>
                  <Link href="/sign-in">Sign In</Link>
                </Button>
                <Button
                  className="bg-gradient-to-r from-purple-600 to-blue-600"
                  asChild
                >
                  <Link href="/sign-up">Try It Free</Link>
                </Button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
