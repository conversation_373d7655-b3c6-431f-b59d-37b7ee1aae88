'use client';
import { createContext, ReactNode, useContext, useState } from 'react';

const RefetchContext = createContext<{
  refetchSignal: number;
  triggerRefetch: () => void;
}>({ refetchSignal: 0, triggerRefetch: () => {} });

export function useRefetchTrigger() {
  return useContext(RefetchContext);
}

export function RefetchProvider({ children }: { children: ReactNode }) {
  const [refetchSignal, setRefetchSignal] = useState(0);
  const triggerRefetch = () => setRefetchSignal((s) => s + 1);
  return (
    <RefetchContext.Provider value={{ refetchSignal, triggerRefetch }}>
      {children}
    </RefetchContext.Provider>
  );
}
