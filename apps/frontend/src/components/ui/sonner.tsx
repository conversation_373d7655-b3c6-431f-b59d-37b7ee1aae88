"use client";
import {
  AlertTriangleIcon,
  BellIcon,
  CircleCheckIcon,
  InfoIcon,
  XCircleIcon,
} from "lucide-react";

import { useTheme } from "next-themes";
import { Toaster as Sonner, toast, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      style={
        {
          "--normal-bg": "var(--popover)",
          "--normal-text": "var(--popover-foreground)",
          "--normal-border": "var(--border)",
        } as React.CSSProperties
      }
      {...props}
    />
  );
};

export function showToast({
  text,
  variant,
}: {
  text: string;
  variant?: "default" | "success" | "error" | "info" | "warning";
}) {
  switch (variant) {
    case "success":
      return toast.success(text, {
        icon: <CircleCheckIcon className="size-4" />,
        style: {
          backgroundColor: "var(--success)",
          color: "var(--background)",
        },
      });
    case "error":
      return toast.error(text, {
        icon: <XCircleIcon className="size-4" />,
        style: {
          backgroundColor: "var(--destructive)",
          color: "var(--background)",
        },
      });
    case "info":
      return toast.info(text, {
        icon: <InfoIcon className="size-4" />,
        style: {
          backgroundColor: "var(--info)",
          color: "var(--background)",
        },
      });
    case "warning":
      return toast.warning(text, {
        icon: <AlertTriangleIcon className="size-4" />,
        style: {
          backgroundColor: "var(--warning)",
          color: "var(--background)",
        },
      });
    default:
      return toast(text, {
        icon: <BellIcon className="size-4" />,
        style: {
          backgroundColor: "var(--background)",
          color: "var(--primary)",
        },
      });
  }
}

export { Toaster };
