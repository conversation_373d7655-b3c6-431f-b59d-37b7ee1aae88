'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useQuery } from '@tanstack/react-query';
import { AlertTriangle, Eye, Mail, Users } from 'lucide-react';
import { useState } from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  YAxis,
} from 'recharts';
import VerificationPreviewSheet, { UsageRecord } from './dashboard-new/preview';
import { VerificationHistoryTable } from './dashboard-new/verification-history-table';
import { useRefetchTrigger } from './refetch-context';

export const Dashboard = () => {
  // Email verification state
  const [email, setEmail] = useState('');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const apiUrl = process.env.NEXT_PUBLIC_BASE_URL + '/api/verification';

  const [timeframe, setTimeframe] = useState<
    'today' | 'yesterday' | '7d' | '30d'
  >('today');
  const { refetchSignal, triggerRefetch } = useRefetchTrigger();

  // Fetch dashboard stats and chart data
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats', timeframe, refetchSignal],
    queryFn: async () => {
      const res = await fetch(`/dashboard/api/stats?timeframe=${timeframe}`);
      if (!res.ok) throw new Error('Failed to fetch stats');
      return res.json();
    },
  });

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewRecord, setPreviewRecord] = useState<UsageRecord | null>(null);

  const handleCheck = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }
    setLoading(true);
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      const data = await response.json();
      if (response.ok) {
        setResult(data);
        setError('');
        triggerRefetch();
      } else {
        setError(data.error || 'Failed to check email');
        setResult(null);
      }
    } catch (err) {
      setError('Failed to check email');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = () => {
    if (!result) return;
    // Map result to UsageRecord shape
    setPreviewRecord({
      id: result.id || 'single',
      createdAt: result.createdAt || new Date().toISOString(),
      email: result.email || email,
      action: (result.action || '').toUpperCase(),
      riskScore: result.emailRiskScore ?? 0,
      reasons: result.reasons || [],
      ipAddress: result.ipAddress,
      apiKey: result.apiKey,
    });
    setPreviewOpen(true);
  };

  return (
    <>
      <div className="min-h-screen w-full overflow-hidden bg-gray-50">
        <header className="sticky top-0 z-30 flex h-16 w-full items-center justify-between border-b bg-white px-4 shadow-sm">
          <div className="flex items-center gap-2">
            <SidebarTrigger />
            <span className="text-lg font-bold text-gray-900">Dashboard</span>
          </div>
          {/* Timeframe selector: only visible on large screens */}
          <div className="hidden items-center space-x-2 text-sm text-gray-600 lg:flex">
            <span>Choose date</span>
            <div className="flex space-x-1">
              <Button
                variant={timeframe === '30d' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('30d')}
              >
                30d
              </Button>
              <Button
                variant={timeframe === '7d' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('7d')}
              >
                7d
              </Button>
              <Button
                variant={timeframe === 'today' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('today')}
              >
                Today
              </Button>
              <Button
                variant={timeframe === 'yesterday' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('yesterday')}
              >
                Yesterday
              </Button>
            </div>
          </div>
        </header>
        <main className="container mx-auto space-y-6 px-4 py-8">
          {/* Timeframe selector for small screens */}
          <div className="mb-4 flex items-center space-x-2 text-sm text-gray-600 lg:hidden">
            <span>Choose date</span>
            <div className="flex space-x-1">
              <Button
                variant={timeframe === '30d' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('30d')}
              >
                30d
              </Button>
              <Button
                variant={timeframe === '7d' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('7d')}
              >
                7d
              </Button>
              <Button
                variant={timeframe === 'today' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('today')}
              >
                Today
              </Button>
              <Button
                variant={timeframe === 'yesterday' ? 'secondary' : 'outline'}
                size="sm"
                onClick={() => setTimeframe('yesterday')}
              >
                Yesterday
              </Button>
            </div>
          </div>
          {/* Stats Cards */}
          <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Users denied
                </CardTitle>
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">
                  {statsLoading ? '--' : (statsData?.usersDenied ?? 0)}
                </div>
                {/* Optionally add percent change here if available */}
              </CardContent>
              <div className="absolute top-0 right-0 bottom-0 w-1 bg-red-500"></div>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  User checks done
                </CardTitle>
                <Users className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">
                  {statsLoading ? '--' : (statsData?.checksDone ?? 0)}
                </div>
                <p className="mt-1 text-sm text-gray-600">Total validations</p>
              </CardContent>
              <div className="absolute top-0 right-0 bottom-0 w-1 bg-green-500"></div>
            </Card>

            <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  API Calls
                </CardTitle>
                <Mail className="h-4 w-4 text-blue-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">
                  {statsLoading ? '--' : (statsData?.apiCalls ?? 0)}
                </div>
              </CardContent>
              <div className="absolute top-0 right-0 bottom-0 w-1 bg-blue-500"></div>
            </Card>

            {/* <Card className="relative overflow-hidden">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Success Rate
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-purple-500" />
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-gray-900">
                  {statsLoading
                    ? '--'
                    : `${(statsData?.successRate ?? 0).toFixed(1)}%`}
                </div>
              </CardContent>
              <div className="absolute top-0 right-0 bottom-0 w-1 bg-purple-500"></div>
            </Card> */}
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Validation Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart
                    data={statsData?.chartData || []}
                    margin={{ left: 10, right: 10, top: 10, bottom: 10 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <YAxis allowDecimals={false} />
                    <Tooltip
                      formatter={(value, name) => [
                        value,
                        name === 'deny' || name === 'quarantine'
                          ? 'Deny/Quarantine'
                          : 'Total',
                      ]}
                      labelFormatter={(label) => `${label}:00`}
                    />
                    <Area
                      type="monotone"
                      dataKey={(row) => row.allow + row.deny + row.quarantine}
                      stroke="#10B981"
                      fill="#10B981"
                      fillOpacity={0.2}
                      name="Total"
                    />
                    <Area
                      type="monotone"
                      dataKey={(row) => row.deny + row.quarantine}
                      stroke="#EF4444"
                      fill="#EF4444"
                      fillOpacity={0.2}
                      name="Deny/Quarantine"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-gray-900">
                  Email Verification Tool
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-gray-600">Check single email</p>
                <div className="flex space-x-2">
                  <Input
                    placeholder="Enter email address..."
                    className="flex-1"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') handleCheck();
                    }}
                    disabled={loading}
                  />
                  <Button
                    onClick={handleCheck}
                    disabled={loading}
                    loading={loading}
                  >
                    {loading ? 'Checking...' : 'Check'}
                  </Button>
                </div>
                {/* Show result below the input */}
                {error && (
                  <div className="mt-2 text-sm text-red-600">{error}</div>
                )}
                {result && (
                  <Card className="mt-4">
                    <CardHeader className="pb-2">
                      <CardTitle className="flex items-center justify-between">
                        <span>Verification Result</span>
                        <Badge
                          variant={
                            result.status === 'valid' &&
                            result.action === 'allow'
                              ? 'success'
                              : result.status === 'valid' &&
                                  result.action !== 'allow'
                                ? 'secondary'
                                : result.status === 'invalid' ||
                                    result.action === 'deny'
                                  ? 'destructive'
                                  : 'secondary'
                          }
                        >
                          {result.status === 'valid' &&
                          result.action === 'allow'
                            ? 'Valid'
                            : result.temporaryEmail
                              ? 'Temporary'
                              : result.status === 'invalid' ||
                                  result.action === 'deny'
                                ? 'Blocked'
                                : result.status}
                        </Badge>
                      </CardTitle>
                      <CardDescription>{result.email || email}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="font-medium">Status:</span>{' '}
                          {result.status}
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Action:</span>{' '}
                          {result.action}
                        </div>
                        {result.temporaryEmail && (
                          <div className="text-sm text-yellow-600">
                            Temporary email detected
                          </div>
                        )}
                        {result.reasons && result.reasons.length > 0 && (
                          <div className="text-sm">
                            <span className="font-medium">Reasons:</span>{' '}
                            {result.reasons.join(', ')}
                          </div>
                        )}
                        <div className="text-xs text-gray-500">
                          Risk Score: {result.emailRiskScore}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
                <div className="mt-6">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handlePreview}
                    disabled={!result}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Preview window
                  </Button>
                  <VerificationPreviewSheet
                    open={previewOpen}
                    onOpenChange={setPreviewOpen}
                    record={previewRecord}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* API Keys Management */}
          <Tabs defaultValue="verification" className="space-y-6">
            <TabsList className="grid w-full grid-cols-1 lg:w-auto lg:grid-cols-1">
              <TabsTrigger value="verification">
                Verification Events
              </TabsTrigger>
            </TabsList>

            <TabsContent value="verification">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold text-gray-900">
                      Recent Verification Events
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <VerificationHistoryTable timeframe={timeframe} />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </>
  );
};
