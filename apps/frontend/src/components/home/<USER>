import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ArrowRight, Check } from 'lucide-react';

export default function CtaSection() {
  const benefits = [
    '100,000 free validations per month',
    'Real-time API access',
    'Fraud detection included',
    '24/7 support',
  ];

  return (
    <section className="relative overflow-hidden py-24">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-blue-600 to-purple-800"></div>
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Animated elements */}
      <div className="animate-pulse-slow absolute top-20 left-10 h-20 w-20 rounded-full bg-white/10 blur-xl"></div>
      <div
        className="animate-pulse-slow absolute right-20 bottom-20 h-32 w-32 rounded-full bg-white/10 blur-xl"
        style={{ animationDelay: '2s' }}
      ></div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="mb-6 text-4xl font-bold text-white lg:text-5xl">
            Ready to Start Validating?
          </h2>
          <p className="mx-auto mb-12 max-w-2xl text-xl text-white/90">
            Join thousands of businesses that trust EmailGuard to protect their
            platforms from fake accounts and improve their email deliverability.
          </p>

          <Card className="mx-auto max-w-2xl border-0 bg-white/95 p-8 shadow-2xl backdrop-blur-sm">
            <div className="space-y-6">
              <div className="grid gap-4 text-left md:grid-cols-2">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Check className="h-5 w-5 flex-shrink-0 text-green-500" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>

              <div className="flex flex-col gap-3 sm:flex-row">
                <Input
                  placeholder="Enter your email to get started"
                  className="h-12 flex-1"
                />
                <Button
                  size="lg"
                  className="h-12 bg-gradient-to-r from-purple-600 to-blue-600 px-8 hover:from-purple-700 hover:to-blue-700"
                >
                  Get Started Free
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <p className="text-sm text-gray-600">
                No credit card required • Start validating immediately
              </p>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
}
