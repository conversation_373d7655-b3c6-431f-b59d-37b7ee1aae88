import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  AlertTriangle,
  BarChart3,
  CheckCircle2,
  Globe,
  Mail,
  Shield,
} from 'lucide-react';

export default function Features() {
  const features = [
    {
      icon: Mail,
      title: 'Email Intelligence',
      description:
        "Identify if the user's email is suspicious or temporary by detecting email quality, checking against our domain database & AI matching.",
      demo: {
        title: 'Live Results',
        items: [
          {
            email: '<EMAIL>',
            result: 'Deny',
            score: 87,
            color: 'red',
          },
          {
            email: '<EMAIL>',
            result: 'Allow',
            score: 12,
            color: 'green',
          },
          {
            email: '<EMAIL>',
            result: 'Allow',
            score: 17,
            color: 'green',
          },
          {
            email: '<EMAIL>',
            result: 'Quarantine',
            score: 55,
            color: 'yellow',
          },
        ],
      },
    },
    {
      icon: Globe,
      title: 'Proxy & VPN Detection',
      description:
        "Analyse your user's IP addresses to determine if they originate from suspicious sources like a VPN or proxy.",
      demo: {
        title: 'IP Analysis',
        email: '<EMAIL>',
        ip: '192.168.1.0304',
        indicators: ['VPN', 'High risk IP'],
        action: 'Deny',
      },
    },
  ];

  const additionalFeatures = [
    {
      icon: BarChart3,
      title: 'Real-time Analytics',
      description: 'Track validation metrics and trends',
    },
    {
      icon: AlertTriangle,
      title: 'Fraud Detection',
      description: 'Advanced algorithms to detect suspicious patterns',
    },
    {
      icon: CheckCircle2,
      title: 'High Accuracy',
      description: '99.9% accurate email validation results',
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-level security for your data',
    },
  ];

  return (
    <section id="features" className="bg-gray-50 py-24">
      <div className="container mx-auto px-4">
        <div className="mb-16 text-center">
          <h2 className="mb-4 text-4xl font-bold text-gray-900">
            Powerful Features for{' '}
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Email Validation
            </span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-gray-600">
            EmailGuard comes feature & data packed. Get dozens of fraud signals
            and real-time analysis results to automate blocking fake signups.
          </p>
        </div>

        {/* Main Features */}
        <div className="mb-16 grid gap-8 lg:grid-cols-2">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="border-0 bg-white p-8 shadow-lg transition-shadow hover:shadow-xl"
            >
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-purple-600 to-blue-600">
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      {feature.title}
                    </h3>
                  </div>
                </div>

                <p className="leading-relaxed text-gray-600">
                  {feature.description}
                </p>

                {/* Email Intelligence Demo */}
                {feature.demo.items && (
                  <div className="rounded-lg bg-gray-50 p-4">
                    <div className="mb-3 flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Email
                      </span>
                      <span className="text-sm font-medium text-gray-700">
                        Results
                      </span>
                      <span className="text-sm font-medium text-gray-700">
                        Score
                      </span>
                    </div>
                    {feature.demo.items.map((item, itemIndex) => (
                      <div
                        key={itemIndex}
                        className="flex items-center justify-between border-b border-gray-200 py-2 last:border-b-0"
                      >
                        <span className="font-mono text-sm text-gray-600">
                          {item.email}
                        </span>
                        <Badge
                          variant={
                            item.color === 'green'
                              ? 'default'
                              : item.color === 'red'
                                ? 'destructive'
                                : 'secondary'
                          }
                        >
                          {item.result}
                        </Badge>
                        <span className="text-sm text-gray-600">
                          {item.score}
                        </span>
                      </div>
                    ))}
                    <div className="mt-3 flex items-center text-xs text-gray-500">
                      <div className="mr-2 h-2 w-2 rounded-full bg-green-400"></div>
                      Duplicate Or Similar Email: 85%
                    </div>
                  </div>
                )}

                {/* Proxy & VPN Demo */}
                {feature.demo.email && (
                  <div className="rounded-lg bg-gray-50 p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span className="font-mono text-sm text-gray-700">
                          {feature.demo.email}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <Shield className="h-4 w-4 text-gray-500" />
                        <Globe className="h-4 w-4 text-gray-500" />
                        <span className="font-mono text-sm text-gray-700">
                          {feature.demo.ip}
                        </span>
                      </div>
                      <div className="flex items-center justify-between pt-2">
                        <div className="flex space-x-2">
                          {feature.demo.indicators.map(
                            (indicator, indIndex) => (
                              <Badge
                                key={indIndex}
                                variant="secondary"
                                className="text-xs"
                              >
                                {indicator}
                              </Badge>
                            )
                          )}
                        </div>
                        <Badge variant="destructive">
                          {feature.demo.action}
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Card>
          ))}
        </div>

        {/* Additional Features Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {additionalFeatures.map((feature, index) => (
            <Card
              key={index}
              className="border-0 bg-white p-6 text-center shadow-lg transition-shadow hover:shadow-xl"
            >
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-gradient-to-r from-purple-600 to-blue-600">
                <feature.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="mb-2 text-lg font-semibold text-gray-900">
                {feature.title}
              </h3>
              <p className="text-sm text-gray-600">{feature.description}</p>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
