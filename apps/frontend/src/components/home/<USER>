import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  AlertTriangle,
  CheckCircle,
  Shield,
  Target,
  XCircle,
  Zap,
} from 'lucide-react';

export default function Hero() {
  const emailExamples = [
    { email: '<EMAIL>', status: 'valid', score: 95 },
    { email: '<EMAIL>', status: 'invalid', score: 12 },
    { email: '<EMAIL>', status: 'risky', score: 45 },
  ];

  const features = [
    {
      icon: Shield,
      title: 'Advanced Protection',
      description: 'Multi-layer validation with real-time threat detection',
    },
    {
      icon: Zap,
      title: 'Lightning Fast',
      description: 'Get results in under 50ms with 99.9% uptime',
    },
    {
      icon: Target,
      title: 'Precision Accuracy',
      description: 'Industry-leading 99.9% accuracy rate',
    },
  ];

  return (
    <section className="relative flex min-h-screen items-center overflow-hidden">
      {/* Animated Background */}
      <div className="gradient-bg animate-gradient absolute inset-0 opacity-20"></div>
      <div className="absolute inset-0">
        <div className="animate-float absolute top-20 left-10 h-20 w-20 rounded-full bg-purple-400 opacity-70 mix-blend-multiply blur-xl filter"></div>
        <div
          className="animate-float absolute top-40 right-20 h-32 w-32 rounded-full bg-blue-400 opacity-70 mix-blend-multiply blur-xl filter"
          style={{ animationDelay: '2s' }}
        ></div>
        <div
          className="animate-float absolute bottom-20 left-20 h-24 w-24 rounded-full bg-pink-400 opacity-70 mix-blend-multiply blur-xl filter"
          style={{ animationDelay: '4s' }}
        ></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-32">
        <div className="grid items-center gap-12 lg:grid-cols-2">
          {/* Left Column - Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-5xl leading-tight font-bold text-gray-900 lg:text-6xl">
                <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  Email Validation
                </span>
                <br />
                Made Simple
              </h1>
              <p className="max-w-lg text-xl leading-relaxed text-gray-600">
                EmailGuard comes feature & data packed. Get dozens of fraud
                signals and real-time analysis results to automate blocking fake
                signups and protect your platform.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex flex-col gap-4 sm:flex-row">
                <Button
                  size="lg"
                  className="h-12 bg-gradient-to-r from-purple-600 to-blue-600 px-8 hover:from-purple-700 hover:to-blue-700"
                >
                  Start Free Trial
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="h-12 border-2 px-8"
                >
                  View Documentation
                </Button>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                {features.map((feature, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 rounded-lg bg-white/60 p-3 backdrop-blur-sm"
                  >
                    <feature.icon className="h-5 w-5 flex-shrink-0 text-purple-600" />
                    <div>
                      <div className="text-sm font-semibold text-gray-900">
                        {feature.title}
                      </div>
                      <div className="text-xs text-gray-600">
                        {feature.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex items-center space-x-8 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">99.9%</div>
                <div className="text-sm text-gray-600">Accuracy</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">1M+</div>
                <div className="text-sm text-gray-600">Emails Validated</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">50ms</div>
                <div className="text-sm text-gray-600">Response Time</div>
              </div>
            </div>
          </div>

          {/* Right Column - Email Validation Preview */}
          <div className="space-y-6">
            <Card className="border-0 bg-white/80 p-6 shadow-2xl backdrop-blur-sm">
              <h3 className="mb-4 text-lg font-semibold text-gray-900">
                Live Validation Results
              </h3>
              <div className="space-y-4">
                <div className="grid grid-cols-12 gap-4 border-b pb-2 text-sm font-medium text-gray-600">
                  <div className="col-span-6">Email Address</div>
                  <div className="col-span-3 text-center">Status</div>
                  <div className="col-span-3 text-center">Score</div>
                </div>
                {emailExamples.map((example, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-12 items-center gap-4 rounded-lg bg-gray-50 p-3"
                  >
                    <div className="col-span-6 flex items-center space-x-3">
                      {example.status === 'valid' && (
                        <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-500" />
                      )}
                      {example.status === 'invalid' && (
                        <XCircle className="h-4 w-4 flex-shrink-0 text-red-500" />
                      )}
                      {example.status === 'risky' && (
                        <AlertTriangle className="h-4 w-4 flex-shrink-0 text-yellow-500" />
                      )}
                      <span className="truncate text-sm font-medium text-gray-700">
                        {example.email}
                      </span>
                    </div>
                    <div className="col-span-3 flex justify-center">
                      <span
                        className={`rounded-full px-2 py-1 text-xs ${
                          example.status === 'valid'
                            ? 'bg-green-100 text-green-800'
                            : example.status === 'invalid'
                              ? 'bg-red-100 text-red-800'
                              : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {example.status}
                      </span>
                    </div>
                    <div className="col-span-3 text-center">
                      <span className="text-sm font-medium text-gray-600">
                        {example.score}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            <div className="grid grid-cols-2 gap-4">
              <Card className="border-0 bg-gradient-to-r from-purple-50 to-blue-50 p-4">
                <div className="text-2xl font-bold text-purple-600">15+</div>
                <div className="text-sm text-gray-600">Validation Checks</div>
              </Card>
              <Card className="border-0 bg-gradient-to-r from-blue-50 to-purple-50 p-4">
                <div className="text-2xl font-bold text-blue-600">
                  Real-time
                </div>
                <div className="text-sm text-gray-600">API Response</div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
