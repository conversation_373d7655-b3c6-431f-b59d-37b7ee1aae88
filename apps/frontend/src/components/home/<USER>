import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Check, Crown, Shield, Star, Zap } from 'lucide-react';

export default function PricingSection() {
  const plans = [
    {
      name: 'Free',
      price: '0',
      period: 'forever',
      description: 'Perfect for testing and small projects',
      validations: '200',
      features: [
        '200 validations/month',
        'Basic email validation',
        'API access',
        'Community support',
        'Standard documentation',
      ],
      buttonText: 'Get Started',
      buttonVariant: 'outline' as const,
      popular: false,
      icon: Zap,
      afterIncluded: {
        rate: '$0.01',
        unit: 'per validation',
      },
    },
    {
      name: 'Starter',
      price: '12.99',
      period: 'month',
      description: 'Ideal for growing businesses with higher volume',
      validations: '20,000',
      features: [
        '20,000 validations/month',
        'Advanced fraud detection',
        'Real-time API',
        'Priority support',
        'Webhook integration',
        'Basic analytics',
      ],
      buttonText: 'Start Free Trial',
      buttonVariant: 'default' as const,
      popular: true,
      icon: Crown,
      afterIncluded: {
        rate: '$0.008',
        unit: 'per validation',
      },
    },
    {
      name: 'Professional',
      price: '79.99',
      period: 'month',
      description: 'For large organizations with enterprise needs',
      validations: '150,000',
      features: [
        '150,000 validations/month',
        'All starter features',
        'Custom integrations',
        'Dedicated support',
        'SLA guarantee',
        'Advanced reporting',
        'Custom validation rules',
        'White-label options',
      ],
      buttonText: 'Contact Sales',
      buttonVariant: 'outline' as const,
      popular: false,
      icon: Shield,
      afterIncluded: {
        rate: '$0.005',
        unit: 'per validation',
      },
    },
  ];

  return (
    <section className="relative overflow-hidden bg-white py-24">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/2 h-96 w-96 -translate-x-1/2 transform rounded-full bg-gradient-to-r from-purple-100 to-blue-100 opacity-70 mix-blend-multiply blur-3xl filter"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mb-16 text-center">
          <Badge variant="outline" className="mb-4 px-3 py-1">
            Pricing
          </Badge>
          <h2 className="mb-6 text-4xl font-bold text-gray-900 lg:text-5xl">
            Choose Your
            <span className="block bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Perfect Plan
            </span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-gray-600">
            Start free with 200 validations per month. Scale as you grow with
            our flexible pricing.
          </p>
        </div>

        <div className="mx-auto grid max-w-7xl gap-8 md:grid-cols-3">
          {plans.map((plan, index) => (
            <Card
              key={index}
              className={`relative overflow-hidden transition-all duration-300 hover:shadow-2xl ${
                plan.popular
                  ? 'scale-105 border-2 border-purple-200 shadow-xl'
                  : 'border border-gray-200 hover:border-purple-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute top-0 right-0 left-0">
                  <div className="bg-gradient-to-r from-purple-600 to-blue-600 py-2 text-center text-sm font-semibold text-white">
                    <Star className="mr-1 inline h-4 w-4" />
                    Most Popular
                  </div>
                </div>
              )}

              <CardHeader
                className={`text-center ${plan.popular ? 'pt-12' : 'pt-6'}`}
              >
                <div className="mb-4 flex items-center justify-center">
                  <div className="rounded-full bg-gradient-to-r from-purple-100 to-blue-100 p-3">
                    <plan.icon className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <CardTitle className="mb-2 text-2xl font-bold text-gray-900">
                  {plan.name}
                </CardTitle>
                <div className="mb-4">
                  <span className="text-5xl font-bold text-gray-900">
                    ${plan.price}
                  </span>
                  <span className="text-gray-600">/{plan.period}</span>
                </div>
                <p className="mb-2 text-sm text-gray-600">{plan.description}</p>
                <div className="text-lg font-semibold text-purple-600">
                  {plan.validations} validations included
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="flex items-center space-x-3"
                    >
                      <Check className="h-5 w-5 flex-shrink-0 text-green-500" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem
                    value="after-included"
                    className="border-gray-200"
                  >
                    <AccordionTrigger className="text-sm font-medium text-gray-700 hover:text-purple-600">
                      After included usage
                    </AccordionTrigger>
                    <AccordionContent className="pt-2 text-sm text-gray-600">
                      <div className="rounded-lg bg-gray-50 p-3">
                        <div className="font-medium text-gray-900">
                          {plan.afterIncluded.rate}
                        </div>
                        <div className="text-xs text-gray-500">
                          {plan.afterIncluded.unit}
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <Button
                  variant={plan.buttonVariant}
                  className={`h-12 w-full text-base font-semibold ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700'
                      : ''
                  }`}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features comparison */}
        <div className="mt-20 text-center">
          <div className="mx-auto grid max-w-4xl gap-8 md:grid-cols-3">
            <div className="flex flex-col items-center space-y-3">
              <div className="rounded-full bg-gradient-to-r from-green-100 to-emerald-100 p-4">
                <Shield className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Enterprise Security
              </h3>
              <p className="text-sm text-gray-600">
                Bank-level encryption and compliance
              </p>
            </div>
            <div className="flex flex-col items-center space-y-3">
              <div className="rounded-full bg-gradient-to-r from-blue-100 to-cyan-100 p-4">
                <Zap className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                Lightning Fast
              </h3>
              <p className="text-sm text-gray-600">
                Sub-50ms response times globally
              </p>
            </div>
            <div className="flex flex-col items-center space-y-3">
              <div className="rounded-full bg-gradient-to-r from-purple-100 to-pink-100 p-4">
                <Star className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">
                24/7 Support
              </h3>
              <p className="text-sm text-gray-600">
                Always here when you need us
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
