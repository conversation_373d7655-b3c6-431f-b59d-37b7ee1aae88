import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Alert<PERSON>riangle,
  Bot,
  Shield,
  Target,
  Trash2,
  TrendingDown,
  UserX,
  Zap,
} from 'lucide-react';

export default function UseCasesSection() {
  const useCases = [
    {
      icon: Trash2,
      title: 'Temporary Email Detection',
      description:
        'Automatically identify and block temporary email services that users employ to bypass your signup process.',
      benefits: [
        'Block 10min mail services',
        'Prevent throwaway accounts',
        'Improve user quality',
      ],
      gradient: 'from-red-500 to-pink-500',
      bgGradient: 'from-red-50 to-pink-50',
    },
    {
      icon: UserX,
      title: 'Free Plan Exploitation',
      description:
        'Stop users from creating multiple accounts to abuse your free tier limits and promotional offers.',
      benefits: [
        'Detect duplicate users',
        'Prevent trial abuse',
        'Protect revenue',
      ],
      gradient: 'from-orange-500 to-red-500',
      bgGradient: 'from-orange-50 to-red-50',
    },
    {
      icon: <PERSON><PERSON>,
      title: 'Automated Account Creation',
      description:
        'Identify and prevent bot-generated accounts that can flood your platform with spam and fake activity.',
      benefits: [
        'Block bot networks',
        'Reduce spam signups',
        'Maintain platform integrity',
      ],
      gradient: 'from-blue-500 to-cyan-500',
      bgGradient: 'from-blue-50 to-cyan-50',
    },
    {
      icon: TrendingDown,
      title: 'Domain Reputation Monitoring',
      description:
        'Detect emails from domains with declining reputation scores that may indicate compromised or suspicious sources.',
      benefits: ['Monitor domain health', 'Prevent fraud', 'Enhance security'],
      gradient: 'from-purple-500 to-indigo-500',
      bgGradient: 'from-purple-50 to-indigo-50',
    },
  ];

  const stats = [
    { value: '95%', label: 'Fraud Reduction', icon: Shield },
    { value: '80%', label: 'False Positives Eliminated', icon: Target },
    { value: '50ms', label: 'Average Response Time', icon: Zap },
    { value: '99.9%', label: 'Uptime Guarantee', icon: AlertTriangle },
  ];

  return (
    <section className="relative overflow-hidden bg-gradient-to-b from-gray-50 to-white py-24">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute top-20 left-10 h-72 w-72 rounded-full bg-purple-200 mix-blend-multiply blur-xl filter"></div>
        <div className="absolute right-10 bottom-20 h-72 w-72 rounded-full bg-blue-200 mix-blend-multiply blur-xl filter"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4">
        <div className="mb-16 text-center">
          <Badge variant="outline" className="mb-4 px-3 py-1">
            Use Cases
          </Badge>
          <h2 className="mb-6 text-4xl font-bold text-gray-900 lg:text-5xl">
            Protect Your Platform From
            <span className="block bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Common Threats
            </span>
          </h2>
          <p className="mx-auto max-w-3xl text-xl text-gray-600">
            EmailGuard helps you identify and prevent the most common
            email-based threats that can compromise your platform&apos;s
            integrity and revenue.
          </p>
        </div>

        {/* Use Cases Grid */}
        <div className="mb-20 grid gap-8 md:grid-cols-2">
          {useCases.map((useCase, index) => (
            <Card
              key={index}
              className="group overflow-hidden border-0 bg-white/80 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl"
            >
              <div className={`h-2 bg-gradient-to-r ${useCase.gradient}`}></div>
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-4">
                  <div
                    className={`rounded-lg bg-gradient-to-r p-3 ${useCase.bgGradient}`}
                  >
                    <useCase.icon
                      className={`h-6 w-6 bg-gradient-to-r ${useCase.gradient} bg-clip-text text-transparent`}
                    />
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900 transition-colors group-hover:text-purple-600">
                    {useCase.title}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="leading-relaxed text-gray-600">
                  {useCase.description}
                </p>
                <div className="space-y-2">
                  <h4 className="text-sm font-semibold text-gray-900">
                    Key Benefits:
                  </h4>
                  <ul className="space-y-1">
                    {useCase.benefits.map((benefit, benefitIndex) => (
                      <li
                        key={benefitIndex}
                        className="flex items-center space-x-2 text-sm text-gray-600"
                      >
                        <div
                          className={`h-1.5 w-1.5 rounded-full bg-gradient-to-r ${useCase.gradient}`}
                        ></div>
                        <span>{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats Section */}
        <div className="rounded-2xl border border-gray-100 bg-white/80 p-8 shadow-xl backdrop-blur-sm">
          <div className="mb-8 text-center">
            <h3 className="mb-2 text-2xl font-bold text-gray-900">
              Proven Results
            </h3>
            <p className="text-gray-600">
              See the impact EmailGuard makes for businesses like yours
            </p>
          </div>
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            {stats.map((stat, index) => (
              <div key={index} className="group text-center">
                <div className="mb-3 flex justify-center">
                  <div className="rounded-full bg-gradient-to-r from-purple-100 to-blue-100 p-3 transition-colors group-hover:from-purple-200 group-hover:to-blue-200">
                    <stat.icon className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
                <div className="mb-1 text-3xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
