export function getDateRangeForTimeframe(timeframe: string) {
  const now = new Date();
  let from: Date;
  let to: Date;
  switch (timeframe) {
    case 'today':
      from = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      to = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case 'yesterday':
      from = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
      to = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case '7d':
      from = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 6);
      to = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    case '30d':
      from = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 29);
      to = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      break;
    default:
      from = new Date(0);
      to = new Date();
  }
  return { from, to };
}
