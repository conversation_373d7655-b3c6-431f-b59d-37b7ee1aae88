import { auth } from '@/lib/auth';
import { generateApiKeyString } from '@/lib/db';
import { prisma } from 'database';

async function main() {
  // Create or find the default free plan
  const freePlan = await prisma.plan.upsert({
    where: { name: 'Free' },
    update: {},
    create: {
      name: 'Free',
      description: 'Free plan with 200 monthly credits',
      monthlyCredits: 200,
    },
  });

  // Create the user (do not pass planId here)
  const data = await auth.api.signUpEmail({
    body: {
      email: process.env.DEMO_USER_EMAIL || '<EMAIL>',
      password: process.env.DEMO_USER_PASSWORD || 'securePassword123',
      name: 'Demo User',
      planId: freePlan.id,
    },
  });

  // Update the user to set planId and credits
  await prisma.user.update({
    where: { id: data.user.id },
    data: { planId: freePlan.id, credits: 200 },
  });

  const project = await prisma.project.create({
    data: {
      name: 'New Project',
      description: 'Project Description',
      userId: data.user.id,
    },
  });

  const apiKey = await prisma.apiKey.create({
    data: {
      name: 'API Key',
      key: generateApiKeyString(),
      projectId: project.id,
      createdBy: data.user.id,
    },
  });

  console.log('Seeding completed successfully!');
  console.log('API Key:', apiKey.key);
  console.log('User Email:', process.env.DEMO_USER_EMAIL || '<EMAIL>');
}

main().catch((error) => {
  console.error('An error occurred during database seeding:', error);
  process.exit(1);
});
