import { ActivityType, RuleListType, RuleType } from 'database';
import prisma from '../prisma';
import { createActivity } from './activities';

// Default validation rules (matching defaultRules.json from Hono API)
const DEFAULT_VALIDATION_RULES = {
  checkDisposable: true,
  checkForMxRecords: true,
  checkSyntax: true,
  checkRoleBasedAccounts: false,
  checkPatterns: true,
};

export type ValidationRules = {
  checkDisposable: boolean;
  checkForMxRecords: boolean;
  checkSyntax: boolean;
  checkRoleBasedAccounts: boolean;
  checkPatterns: boolean;
};

/**
 * Add a new rule entry (whitelist/blacklist)
 * @param data - Rule entry data
 * @returns Promise<RuleEntry> - Created rule entry
 */
export async function addRuleEntry(data: {
  projectId: string;
  type: 'EMAIL' | 'DOMAIN';
  value: string;
  listType: 'WHITELIST' | 'BLACKLIST';
  userId?: string;
}) {
  try {
    const ruleEntry = await prisma.ruleEntry.create({
      data: {
        projectId: data.projectId,
        type: data.type as RuleType,
        value: data.value,
        listType: data.listType as RuleListType,
      },
    });

    // Log the activity
    if (data.userId) {
      await createActivity({
        projectId: data.projectId,
        userId: data.userId,
        action:
          data.listType === 'WHITELIST'
            ? ActivityType.UPDATE_WHITELIST
            : ActivityType.UPDATE_BLACKLIST,
      });
    }

    return ruleEntry;
  } catch (error) {
    console.error('Failed to add rule entry:', error);
    throw new Error('Failed to add rule entry');
  }
}

/**
 * Delete a rule entry
 * @param ruleId - The rule ID to delete
 * @param projectId - The project ID for authorization
 * @param userId - The user making the change (for activity logging)
 * @returns Promise<void>
 */
export async function deleteRuleEntry(
  ruleId: string,
  projectId: string,
  userId?: string
): Promise<void> {
  try {
    const deleted = await prisma.ruleEntry.deleteMany({
      where: {
        id: ruleId,
        projectId, // Ensure user can only delete rules from their own project
      },
    });

    if (deleted.count === 0) {
      throw new Error('Rule not found or access denied');
    }

    // Log the activity
    if (userId) {
      await createActivity({
        projectId,
        userId,
        action: ActivityType.UPDATE_WHITELIST, // Using existing activity type
      });
    }
  } catch (error) {
    console.error('Failed to delete rule entry:', error);
    throw new Error('Failed to delete rule');
  }
}

/**
 * Get merged validation rules for a project (defaults + overrides)
 * @param projectId - The project ID
 * @returns Promise<ValidationRules> - Merged validation rules
 */
export async function getProjectValidationRules(
  projectId: string
): Promise<ValidationRules> {
  try {
    const projectRules = await prisma.projectValidationRules.findUnique({
      where: { projectId },
      select: {
        checkDisposable: true,
        checkForMxRecords: true,
        checkSyntax: true,
        checkRoleBasedAccounts: true,
        checkPatterns: true,
      },
    });

    // If no custom rules exist, return defaults
    if (!projectRules) {
      return DEFAULT_VALIDATION_RULES;
    }

    // Merge defaults with overrides (only override if explicitly set)
    return {
      checkDisposable:
        projectRules.checkDisposable ??
        DEFAULT_VALIDATION_RULES.checkDisposable,
      checkForMxRecords:
        projectRules.checkForMxRecords ??
        DEFAULT_VALIDATION_RULES.checkForMxRecords,
      checkSyntax:
        projectRules.checkSyntax ?? DEFAULT_VALIDATION_RULES.checkSyntax,
      checkRoleBasedAccounts:
        projectRules.checkRoleBasedAccounts ??
        DEFAULT_VALIDATION_RULES.checkRoleBasedAccounts,
      checkPatterns:
        projectRules.checkPatterns ?? DEFAULT_VALIDATION_RULES.checkPatterns,
    };
  } catch (error) {
    console.error('Failed to get project validation rules:', error);
    // Return defaults on error
    return DEFAULT_VALIDATION_RULES;
  }
}

/**
 * Update project validation rules (only store differences from defaults)
 * @param projectId - The project ID
 * @param rules - The validation rules to update
 * @param userId - The user making the change (for activity logging)
 * @returns Promise<ValidationRules> - Updated merged validation rules
 */
export async function updateProjectValidationRules(
  projectId: string,
  rules: Partial<ValidationRules>,
  userId?: string
): Promise<ValidationRules> {
  try {
    // Calculate which rules differ from defaults
    const rulesToStore: {
      checkDisposable?: boolean;
      checkForMxRecords?: boolean;
      checkSyntax?: boolean;
      checkRoleBasedAccounts?: boolean;
      checkPatterns?: boolean;
    } = {};

    if (
      rules.checkDisposable !== undefined &&
      rules.checkDisposable !== DEFAULT_VALIDATION_RULES.checkDisposable
    ) {
      rulesToStore.checkDisposable = rules.checkDisposable;
    }
    if (
      rules.checkForMxRecords !== undefined &&
      rules.checkForMxRecords !== DEFAULT_VALIDATION_RULES.checkForMxRecords
    ) {
      rulesToStore.checkForMxRecords = rules.checkForMxRecords;
    }
    if (
      rules.checkSyntax !== undefined &&
      rules.checkSyntax !== DEFAULT_VALIDATION_RULES.checkSyntax
    ) {
      rulesToStore.checkSyntax = rules.checkSyntax;
    }
    if (
      rules.checkRoleBasedAccounts !== undefined &&
      rules.checkRoleBasedAccounts !==
        DEFAULT_VALIDATION_RULES.checkRoleBasedAccounts
    ) {
      rulesToStore.checkRoleBasedAccounts = rules.checkRoleBasedAccounts;
    }
    if (
      rules.checkPatterns !== undefined &&
      rules.checkPatterns !== DEFAULT_VALIDATION_RULES.checkPatterns
    ) {
      rulesToStore.checkPatterns = rules.checkPatterns;
    }

    // If no rules differ from defaults, delete the record to save space
    if (Object.keys(rulesToStore).length === 0) {
      await prisma.projectValidationRules.deleteMany({
        where: { projectId },
      });
    } else {
      // Upsert the rules (only store differences)
      await prisma.projectValidationRules.upsert({
        where: { projectId },
        create: {
          projectId,
          ...rulesToStore,
        },
        update: {
          ...rulesToStore,
          // Explicitly set to null any rules that are now back to defaults
          ...(rules.checkDisposable ===
            DEFAULT_VALIDATION_RULES.checkDisposable && {
            checkDisposable: null,
          }),
          ...(rules.checkForMxRecords ===
            DEFAULT_VALIDATION_RULES.checkForMxRecords && {
            checkForMxRecords: null,
          }),
          ...(rules.checkSyntax === DEFAULT_VALIDATION_RULES.checkSyntax && {
            checkSyntax: null,
          }),
          ...(rules.checkRoleBasedAccounts ===
            DEFAULT_VALIDATION_RULES.checkRoleBasedAccounts && {
            checkRoleBasedAccounts: null,
          }),
          ...(rules.checkPatterns ===
            DEFAULT_VALIDATION_RULES.checkPatterns && { checkPatterns: null }),
        },
      });
    }

    // Log the activity
    if (userId) {
      await createActivity({
        projectId,
        userId,
        action: ActivityType.UPDATE_CUSTOM_RULES,
      });
    }

    // Return the merged rules
    return await getProjectValidationRules(projectId);
  } catch (error) {
    console.error('Failed to update project validation rules:', error);
    throw new Error('Failed to update validation rules');
  }
}

/**
 * Get all rule entries for a project
 * @param projectId - The project ID
 * @returns Promise<RuleEntry[]> - All rule entries for the project
 */
export async function getProjectRules(projectId: string) {
  try {
    return await prisma.ruleEntry.findMany({
      where: {
        projectId,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  } catch (error) {
    console.error('Failed to get project rules:', error);
    return [];
  }
}

/**
 * Get project whitelist and blacklist data formatted for the core API
 * @param projectId - The project ID
 * @returns Object with userWhitelist and userBlacklist arrays formatted for the API
 */
export async function getProjectRulesForApi(projectId: string) {
  try {
    const rules = await prisma.ruleEntry.findMany({
      where: {
        projectId,
      },
      select: {
        type: true,
        value: true,
        listType: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const userWhitelist = rules
      .filter((rule) => rule.listType === RuleListType.WHITELIST)
      .map((rule) => ({
        value: rule.value,
        type: rule.type.toLowerCase() as 'email' | 'domain',
      }));

    const userBlacklist = rules
      .filter((rule) => rule.listType === RuleListType.BLACKLIST)
      .map((rule) => ({
        value: rule.value,
        type: rule.type.toLowerCase() as 'email' | 'domain',
      }));

    return {
      userWhitelist,
      userBlacklist,
    };
  } catch (error) {
    console.error('Failed to get project rules for API:', error);
    return {
      userWhitelist: [],
      userBlacklist: [],
    };
  }
}
