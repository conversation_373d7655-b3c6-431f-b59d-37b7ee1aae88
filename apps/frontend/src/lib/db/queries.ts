'use server';

import { auth } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { headers } from 'next/headers';

/**
 * Retrieves the currently authenticated user
 * @returns User object or null if not authenticated
 */
export async function getUser() {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.user?.id) {
    return null;
  }

  return await prisma.user.findUnique({
    where: { id: session.user.id },
  });
}

/**
 * Gets the primary project for a user
 * @param userId - User ID
 * @returns Project object or null if not in any project
 */
export async function getProjectForUser(userId: string) {
  // As we are not letting users create projects, we can assume that the first project is the one they are part of
  const project = await prisma.project.findFirst({
    where: {
      userId,
    },
  });
  return project || null;
}

/**
 * Gets a project by ID if user owns it
 * @param projectId - Project ID to retrieve
 * @param userId - User ID to check ownership
 * @returns Project object or null if not found/not the owner
 */
export async function getUserProject(projectId: string, userId: string) {
  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId,
    },
  });

  return project || null;
}

/**
 * Checks if a user has permission to access a project
 * @param userId - User ID to check
 * @param projectId - Project ID to check against
 * @returns Boolean indicating if user has permission
 */
export async function hasProjectPermission(userId: string, projectId: string) {
  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId,
    },
  });

  return !!project;
}

/**
 * Gets all projects owned by a user
 * @param userId - User ID to check
 * @returns Array of projects owned by the user
 */
export async function getProjectsByUser(userId: string) {
  const projects = await prisma.project.findMany({
    where: {
      userId,
    },
  });

  return projects;
}

/**
 * Gets a user with their associated projects
 * @param userId - User ID to check
 * @returns User object with projects or null if not found
 */
export async function getUserWithProjects(userId: string) {
  const userWithProjects = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      projects: true,
    },
  });

  return userWithProjects;
}
