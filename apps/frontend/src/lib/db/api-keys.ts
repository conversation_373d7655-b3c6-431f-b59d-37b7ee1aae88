import crypto from 'crypto';
import { ActivityType } from 'database';
import prisma from '../prisma';
import { createActivity } from './activities';

/**
 * Generate a new API key for a team
 * @param projectId Project ID
 * @param userId User ID of the creator
 * @param name Name/description of the API key
 * @param ipAddress IP address of the creator
 * @param expiresAt Optional expiration date
 * @returns Created API key object
 */
export async function createApiKey(
  projectId: string,
  userId: string,
  name: string,
  ipAddress?: string | null,
  expiresAt?: Date
) {
  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId,
    },
  });

  if (!project) {
    throw new Error('Unauthorized: Project not found or user not authorized');
  }

  const keyValue = generateApiKeyString();

  const apiKey = await prisma.apiKey.create({
    data: {
      name,
      key: keyValue,
      projectId,
      createdBy: userId,
      expiresAt,
    },
  });

  await createActivity({
    projectId,
    userId,
    action: ActivityType.CREATE_API_KEY,
    ipAddress,
  });

  return {
    ...apiKey,
    key: keyValue,
  };
}

/**
 * Get all API keys for a project
 * @param projectId Project ID
 * @param includeRevoked Whether to include revoked keys (default: false)
 * @returns Array of API keys
 */
export async function getProjectApiKeys(
  projectId: string,
  includeRevoked: boolean = false
) {
  const where = {
    projectId,
    ...(includeRevoked ? {} : { revokedAt: null }),
  };

  const apiKeys = await prisma.apiKey.findMany({
    where,
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  return apiKeys.map((key) => ({
    ...key,
    key: maskApiKey(key.key),
  }));
}

/**
 * Revoke (disable) an API key
 * @param id API key ID
 * @param projectId Project ID
 * @param userId User ID performing the action
 * @param ipAddress IP address of the user
 * @returns Updated API key or null if not found
 */
export async function revokeApiKey(
  id: string,
  projectId: string,
  userId: string,
  ipAddress?: string | null
) {
  const apiKey = await prisma.apiKey.findFirst({
    where: {
      id,
      projectId,
      revokedAt: null,
    },
  });

  if (!apiKey) {
    return null;
  }

  const project = await prisma.project.findFirst({
    where: {
      id: projectId,
      userId,
    },
  });

  if (!project) {
    throw new Error('Unauthorized: Project not found or user not authorized');
  }

  const revokedKey = await prisma.apiKey.update({
    where: { id },
    data: {
      revokedAt: new Date(),
    },
  });

  await createActivity({
    projectId,
    userId,
    action: ActivityType.REVOKE_API_KEY,
    ipAddress,
  });

  return {
    ...revokedKey,
    key: maskApiKey(revokedKey.key),
  };
}

/**
 * Verify an API key from the request
 * @param keyString The API key string to verify
 * @returns The API key details if valid, null if invalid
 */
export async function verifyApiKey(keyString: string) {
  if (!keyString) return null;

  const apiKey = await prisma.apiKey.findFirst({
    where: {
      key: keyString,
      revokedAt: null,
    },
  });

  if (!apiKey) return null;

  if (apiKey.expiresAt && apiKey.expiresAt < new Date()) return null;

  await prisma.apiKey.update({
    where: { id: apiKey.id },
    data: { lastUsedAt: new Date() },
  });

  return apiKey;
}

/**
 * Generate a random API key string
 * Format: sk_xxxxxxxxxxxxxxxxxxxxxxxxxxxx
 */
export function generateApiKeyString() {
  const prefix = 'sk_';
  const randomBytes = crypto.randomBytes(24).toString('hex');
  return `${prefix}${randomBytes}`;
}

/**
 * Mask API key for display purposes
 * @param key Full API key
 * @returns Masked key (sk_xxxx...xxxx)
 */
function maskApiKey(key: string) {
  if (!key) return '';

  const parts = key.split('_');
  if (parts.length < 3) return key;

  const prefix = parts.slice(0, 2).join('_') + '_';
  const hash = parts.slice(2).join('_');

  if (hash.length <= 8) return key;

  return `${prefix}${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
}
