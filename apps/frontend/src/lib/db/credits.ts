import prisma from '../prisma';

/**
 * Deducts one credit from the user. Throws if no credits left.
 * Uses a transaction for safety in concurrent requests.
 * Note: Prisma does not support row-level locking directly; transaction is best-effort.
 */
export async function deductUserCredit(userId: string): Promise<void> {
  await prisma.$transaction(async (tx) => {
    const user = await tx.user.findUnique({
      where: { id: userId },
      select: { credits: true },
      // No lock option; Prisma does not support row-level locking
    });
    if (!user) throw new Error('User not found');
    if (user.credits <= 0) throw new Error('No credits left');
    await tx.user.update({
      where: { id: userId },
      data: { credits: { decrement: 1 } },
    });
  });
}

/**
 * Refunds one credit to the user. Used when a validation fails or errors occur after deduction.
 */
export async function refundUserCredit(userId: string): Promise<void> {
  await prisma.user.update({
    where: { id: userId },
    data: { credits: { increment: 1 } },
  });
}
