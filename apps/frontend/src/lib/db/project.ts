import { ActivityType, Project } from 'database';
import prisma from '../prisma';
import { createActivity } from './activities';

/**
 * Create a new project for a user
 * @param userId User ID
 * @param name Project name
 * @param description Project description
 */
export async function createProject({
  name,
  description,
  userId,
}: {
  name?: string;
  description?: string;
  userId: string;
}): Promise<Project> {
  const project = await prisma.project.create({
    data: {
      name,
      description,
      userId,
    },
  });
  await createActivity({
    userId,
    projectId: project.id,
    action: ActivityType.CREATE_PROJECT,
  });
  return project;
}
