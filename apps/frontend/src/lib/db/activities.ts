import { ActivityType } from 'database';
import { headers } from 'next/headers';
import prisma from '../prisma';

/**
 * Log an activity for a user/project
 * @param data Activity log data
 * @returns Created activity log
 */
export async function createActivity({
  userId,
  action,
  ipAddress,
  projectId,
}: {
  projectId?: string;
  userId?: string | null;
  action: ActivityType;
  ipAddress?: string | null;
}) {
  const headersList = await headers();
  const ip = headersList.get('x-forwarded-for') || headersList.get('x-real-ip');

  return await prisma.activityLog.create({
    data: {
      projectId,
      userId,
      action,
      ipAddress: ip || ipAddress || '',
      timestamp: new Date(),
    },
  });
}

/**
 * Get all activity logs for a project
 * @param projectId Project ID
 * @param limit Number of logs to return (default: 100)
 * @returns Array of activity logs
 */
export async function getProjectActivityLogs(
  projectId: string,
  limit: number = 100
) {
  return await prisma.activityLog.findMany({
    where: {
      projectId,
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  });
}

/**
 * Get all activity logs for a user
 * @param userId User ID
 * @param limit Number of logs to return (default: 100)
 * @returns Array of activity logs
 */
export async function getUserActivityLogs(userId: string, limit: number = 100) {
  return await prisma.activityLog.findMany({
    where: {
      userId,
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  });
}

/**
 * Get activity logs for a project filtered by actions
 * @param projectId Project ID
 * @param actions Array of activity types to filter by
 * @param limit Number of logs to return (default: 100)
 * @returns Array of filtered activity logs
 */
export async function getFilteredActivityLogs(
  projectId: string,
  actions: ActivityType[],
  limit: number = 100
) {
  return await prisma.activityLog.findMany({
    where: {
      projectId,
      action: {
        in: actions,
      },
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
    },
    orderBy: {
      timestamp: 'desc',
    },
    take: limit,
  });
}
