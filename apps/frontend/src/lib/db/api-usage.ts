import prisma from '@/lib/prisma';
import { createHash } from 'crypto';
import { SignalAction } from 'database';

type UsageHistoryParams = {
  apiKeyId?: string;
  userId?: string;
  projectId: string;
  email: string;
  action: SignalAction;
  riskScore: number;
  reasons?: string[];
  metadata?: Record<string, any>;
  ipAddress?: string | null;
  errorMessage?: string | null;
};

export function generateParamsHash(params: Record<string, any>): string {
  return createHash('sha256').update(JSON.stringify(params)).digest('hex');
}

/**
 * Tracks email validation API usage
 * @param params - The parameters to track
 * @returns The created API usage record
 */
export async function trackValidationApiUsage(params: UsageHistoryParams) {
  return await prisma.usageHistory.create({
    data: {
      apiKeyId: params.apiKeyId,
      userId: params.userId,
      projectId: params.projectId,
      email: params.email,
      action: params.action,
      riskScore: params.riskScore,
      reasons: params.reasons || [],
      metadata: params.metadata,
      ipAddress: params.ipAddress || null,
      errorMessage: params.errorMessage || null,
    },
  });
}

/**
 * Retrieves API usage statistics for a specific project
 * Like total API calls, categorized by action types
 * @param projectId - The project ID to get stats for
 * @param options - Optional filters: from, to (dates)
 * @returns Statistics about API usage
 */
export async function getApiUsageStats(
  projectId: string,
  options?: { from?: Date; to?: Date }
) {
  const where: any = { projectId };
  if (options?.from || options?.to) {
    where.createdAt = {};
    if (options.from) where.createdAt.gte = options.from;
    if (options.to) where.createdAt.lte = options.to;
  }
  // Get counts by action type
  const counts = await prisma.usageHistory.groupBy({
    by: ['action'],
    where,
    _count: {
      action: true,
    },
  });

  const totalCalls = counts.reduce((acc, curr) => acc + curr._count.action, 0);

  // Get counts by action type
  const allowCount =
    counts.find((c) => c.action === SignalAction.ALLOW)?._count.action || 0;
  const denyCount =
    counts.find((c) => c.action === SignalAction.DENY)?._count.action || 0;
  const quarantineCount =
    counts.find((c) => c.action === SignalAction.QUARANTINE)?._count.action ||
    0;

  return {
    totalCalls,
    allowCount,
    denyCount,
    quarantineCount,
  };
}

/**
 * Retrieves recent API usage history for a specific project
 * @param projectId - The project ID to get usage for
 * @param page - Page number
 * @param pageSize - Number of records per page
 * @param options - Optional filters: action (array), from, to (dates)
 * @returns Recent usage history records
 */
export async function getRecentApiUsage(
  projectId: string,
  page = 1,
  pageSize = 10,
  options?: {
    action?: string[];
    from?: Date;
    to?: Date;
  }
) {
  const skip = (page - 1) * pageSize;
  const where: any = { projectId };
  if (options?.action) {
    where.action = { in: options.action };
  }
  if (options?.from || options?.to) {
    where.createdAt = {};
    if (options.from) where.createdAt.gte = options.from;
    if (options.to) where.createdAt.lte = options.to;
  }

  const [data, total] = await Promise.all([
    prisma.usageHistory.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: { apiKey: true },
      take: pageSize,
      skip,
    }),
    prisma.usageHistory.count({
      where,
    }),
  ]);

  const totalPages = Math.ceil(total / pageSize);

  return {
    data,
    totalPages,
    total,
  };
}
