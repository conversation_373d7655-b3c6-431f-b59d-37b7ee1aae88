import { hashPassword, verifyPassword } from '@/utils/password';
import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';
import { nextCookies } from 'better-auth/next-js';
import prisma from './prisma';

export const auth = betterAuth({
  baseURL: process.env.NEXT_PUBLIC_BASE_URL,
  user: {
    additionalFields: {
      role: {
        type: 'string',
        required: false,
        returned: true,
        input: false,
      },
      planId: {
        type: 'string',
        required: true,
        input: true,
      },
      credits: {
        type: 'number',
        required: true,
        defaultValue: 200,
        input: true,
      },
    },
  },
  database: prismaAdapter(prisma, {
    provider: 'postgresql',
  }),
  emailAndPassword: {
    enabled: true,
    autoSignIn: false,
    password: {
      hash: async (password: string) => {
        return await hashPassword(password);
      },
      verify: async ({ hash, password }) => {
        return await verifyPassword(password, hash);
      },
    },
  },

  plugins: [nextCookies()],
});

export const SessionType = auth.$Infer.Session;
