import { NextRequest } from 'next/server';
import { auth } from '../auth';
import { verifyApiKey } from '../db/api-keys';
import prisma from '../prisma';

/**
 * Parse and validate API key from request headers
 * @param request Next.js request object
 * @returns API key string or null if not found/valid
 */
export async function getApiKeyFromRequest(
  request: NextRequest
): Promise<string | null> {
  const authHeader = request.headers.get('X-API-KEY');
  if (authHeader) {
    return authHeader;
  }

  return null;
}

/**
 * Get request metadata and validate authentication
 * Checks for either API key or authenticated session
 * @param request Next.js request object
 * @returns Request metadata with user/team info or null if unauthenticated
 */
export async function getRequestMetadata(
  request: NextRequest,
  projectId?: string
) {
  // Get IP address and user agent
  const ipAddress =
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    'unknown';

  const userAgent = request.headers.get('user-agent') || 'unknown';

  const apiKeyValue = await getApiKeyFromRequest(request);
  if (apiKeyValue) {
    const apiKey = await verifyApiKey(apiKeyValue);

    if (apiKey) {
      return {
        authenticated: true,
        authenticationType: 'api_key',
        apiKeyId: apiKey.id,
        projectId: apiKey.projectId,
        userId: apiKey.createdBy,
        ipAddress,
        userAgent,
      };
    }
  }
  const session = await auth.api.getSession({
    headers: request.headers,
  });

  if (session?.user) {
    // as we are not letting users create projects, we can assume that the first project is the one they are part of
    const project = await prisma.project.findFirst({
      where: {
        userId: session.user.id,
      },
    });

    if (!project) {
      return {
        authenticated: false,
        authenticationType: 'session',
        userId: session.user.id,
        projectId: null,
        ipAddress,
        userAgent,
      };
    }

    return {
      authenticated: true,
      authenticationType: 'session',
      userId: session.user.id,
      projectId: project.id,
      ipAddress,
      userAgent,
    };
  }

  return null;
}
