import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { getRedisClient } from './redis';

const redis = getRedisClient();

const rateLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rlf-verify',
  points: 10, // 10 requests
  duration: 1, // per 1 second
});

const concurrencyLimiter = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rlf-verify-concurrent',
  points: 5, // 5 concurrent
  duration: 1, // window for concurrency
  execEvenly: false,
  insuranceLimiter: new RateLimiterMemory({ points: 5, duration: 1 }),
});

export function consumeRateLimit(key: string) {
  return rateLimiter.consume(key);
}

export function consumeConcurrencyLimit(key: string) {
  return concurrencyLimiter.consume(key);
}

export function releaseConcurrency(key: string) {
  return concurrencyLimiter.penalty(key, -1);
}
