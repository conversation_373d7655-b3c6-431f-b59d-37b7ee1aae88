# Next-SaaS: Modern SaaS Starter Template

A production-ready SaaS starter template built with Next.js, React, Prisma, and TailwindCSS. Launch your SaaS product quickly with all the essential features ready to go.

## Features

- ⚡️ **Next.js 15** with App Router
- 🔐 **Authentication** with email/password
- 👥 **Team management** with invitations
- 🔑 **API key management** for secure API access
- 📊 **Usage tracking** for monitoring API consumption
- 🎨 **Modern UI** built with TailwindCSS and Radix UI
- 🔄 **Form validation** with React Hook Form and Zod
- 🗃️ **Database management** with Prisma

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- PNPM package manager
- PostgreSQL database

### Installation

1. Clone the repository:

```bash
git clone https://github.com/dataautomators-studio/next-saas-template.git
cd next-saas-template
```

2. Install dependencies:

```bash
pnpm install
```

3. Set up your environment variables:

```bash
cp .env.example .env.local
```

4. Edit `.env.local` with your database credentials and other configuration.

5. Set up the database:

```bash
pnpm db:migrate
pnpm db:generate
```

6. Start the development server:

```bash
pnpm dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Project Structure

```
src/
├── app/                   # Next.js App Router pages
│   ├── (main)/            # Main public pages
│   │   └── (auth)/        # Authentication pages
│   ├── api/               # API routes
│   └── dashboard/         # Dashboard pages
├── components/            # React components
│   └── ui/                # UI components
├── lib/                   # Utility functions
│   ├── api/               # API utilities
│   └── db/                # Database utilities
└── providers/             # React context providers

```

## Customization

### Styling

This project uses TailwindCSS for styling. You can customize the theme in `tailwind.config.js`.

### API Endpoints

Create new API endpoints in the `src/app/api` directory.

### Database Schema

The database schema is defined in `prisma/schema.prisma`. After making changes, run:

```bash
pnpm db:migrate
pnpm db:generate
```
