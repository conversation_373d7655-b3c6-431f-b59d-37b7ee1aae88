{"name": "database", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.10.6", "prisma": "6.7.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "6.7.0"}}