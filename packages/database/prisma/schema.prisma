// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

enum RuleType {
  EMAIL
  DOMAIN
}

enum SignalAction {
  ALLOW
  QUARANTINE
  DENY
}

enum Role {
  OWNER
  ADMIN
  USER
}

enum RuleListType {
  BLACKLIST
  WHITELIST
}

// for email and domain whitelists and blacklists
model RuleEntry {
  id        String       @id @default(cuid())
  type      RuleType
  value     String
  listType  RuleListType
  projectId String
  project   Project      @relation(fields: [projectId], references: [id], onDelete: Cascade)
  createdAt DateTime     @default(now())
  updatedAt DateTime     @default(now()) @updatedAt

  @@index([projectId, value])
  @@map("rule_entry")
}

// for project-specific validation rule overrides (only store if different from defaults)
model ProjectValidationRules {
  id        String  @id @default(cuid())
  projectId String  @unique
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  // Only store values that differ from defaults
  // Default: true - store as false only if user wants to disable
  checkDisposable        Boolean?
  // Default: true - store as false only if user wants to disable  
  checkForMxRecords      Boolean?
  // Default: true - store as false only if user wants to disable
  checkSyntax            Boolean?
  // Default: false - store as true only if user wants to enable
  checkRoleBasedAccounts Boolean?
  // Default: true - store as false only if user wants to disable
  checkPatterns          Boolean?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([projectId])
  @@map("project_validation_rules")
}

model Plan {
  id             String  @id @default(cuid())
  name           String  @unique
  description    String?
  monthlyCredits Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User[]
}

model Project {
  id          String   @id @default(cuid())
  name        String   @default("Default Project")
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  userId                 String
  user                   User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  apiKeys                ApiKey[]
  verifications          UsageHistory[]
  activityLogs           ActivityLog[]
  RuleEntry              RuleEntry[]
  ProjectValidationRules ProjectValidationRules?

  @@index([userId])
  @@map("project")
}

model User {
  id            String         @id @default(cuid())
  name          String?
  email         String
  emailVerified Boolean
  image         String?
  createdAt     DateTime       @default(now())
  deletedAt     DateTime?
  updatedAt     DateTime
  role          Role           @default(USER)
  planId        String
  plan          Plan           @relation(fields: [planId], references: [id])
  credits       Int            @default(200)
  sessions      Session[]
  accounts      Account[]
  projects      Project[]
  activities    ActivityLog[]
  apiKeys       ApiKey[]       @relation("creator")
  verifications UsageHistory[]

  @@unique([email])
  @@index([planId])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model ActivityLog {
  id        String       @id @default(cuid())
  projectId String?
  userId    String?
  action    ActivityType
  timestamp DateTime     @default(now())
  ipAddress String?

  project Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([projectId])
  @@index([userId])
  @@map("activity_log")
}

model ApiKey {
  id         String    @id @default(cuid())
  projectId  String
  name       String
  key        String    @unique
  createdBy  String
  createdAt  DateTime  @default(now())
  lastUsedAt DateTime?
  expiresAt  DateTime?
  revokedAt  DateTime?

  project Project        @relation(fields: [projectId], references: [id], onDelete: Cascade)
  creator User           @relation("creator", fields: [createdBy], references: [id], onDelete: Cascade)
  history UsageHistory[]

  @@index([projectId])
  @@index([createdBy])
  @@map("api_key")
}

model UsageHistory {
  id           String       @id @default(cuid())
  projectId    String
  email        String
  riskScore    Int
  action       SignalAction
  reasons      String[]
  apiKeyId     String?
  userId       String?
  errorMessage String?
  metadata     Json?
  ipAddress    String?
  createdAt    DateTime     @default(now())

  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  apiKey  ApiKey? @relation(fields: [apiKeyId], references: [id], onDelete: SetNull)
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([projectId])
  @@index([apiKeyId])
  @@index([userId])
  @@index([email])
  @@map("verification_history")
}

enum ActivityType {
  SIGN_UP
  SIGN_IN
  SIGN_OUT
  UPDATE_PASSWORD
  DELETE_ACCOUNT
  UPDATE_ACCOUNT
  CREATE_PROJECT
  CREATE_API_KEY
  REVOKE_API_KEY
  UPDATE_WHITELIST
  UPDATE_BLACKLIST
  UPDATE_CUSTOM_RULES
}
