-- CreateTable
CREATE TABLE "project_validation_rules" (
    "id" TEXT NOT NULL,
    "projectId" TEXT NOT NULL,
    "checkDisposable" BOOLEAN,
    "checkForMxRecords" BOOLEAN,
    "checkSyntax" BOOLEAN,
    "checkRoleBasedAccounts" BOOLEAN,
    "checkPatterns" BOOLEAN,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "project_validation_rules_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "project_validation_rules_projectId_key" ON "project_validation_rules"("projectId");

-- CreateIndex
CREATE INDEX "project_validation_rules_projectId_idx" ON "project_validation_rules"("projectId");

-- AddForeignKey
ALTER TABLE "project_validation_rules" ADD CONSTRAINT "project_validation_rules_projectId_fkey" FOREIGN KEY ("projectId") REFERENCES "project"("id") ON DELETE CASCADE ON UPDATE CASCADE;
